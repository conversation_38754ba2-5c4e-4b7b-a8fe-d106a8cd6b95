import React, { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useAuth } from "@/hooks/useAuth";
import { IoPersonAdd, IoArrowBack } from "react-icons/io5";

interface SignupFormData {
  department: string;
  full_name: string;
  username: string;
  password: string;
  confirmPassword: string;
}

const Signup = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { login } = useAuth();
  
  const [formData, setFormData] = useState<SignupFormData>({
    department: "",
    full_name: "",
    username: "",
    password: "",
    confirmPassword: "",
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Extract invite token from URL parameters
  const inviteToken = searchParams.get("access_token");
  const expiresAt = searchParams.get("expires_at");
  const inviteType = searchParams.get("type");

  useEffect(() => {
    // Validate invite parameters
    if (!inviteToken || inviteType !== "invite") {
      setError("Invalid or missing invite link. Please request a new invitation.");
      return;
    }

    // Check if invite has expired
    if (expiresAt && parseInt(expiresAt) * 1000 < Date.now()) {
      setError("This invitation has expired. Please request a new invitation.");
      return;
    }

    // Store invite token temporarily
    sessionStorage.setItem("invite_token", inviteToken);
  }, [inviteToken, expiresAt, inviteType]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    setError(null);
  };

  const validateForm = (): boolean => {
    if (!formData.full_name.trim()) {
      setError("Full name is required");
      return false;
    }
    
    if (!formData.username.trim()) {
      setError("Username is required");
      return false;
    }
    
    if (!formData.department.trim()) {
      setError("Department is required");
      return false;
    }
    
    if (formData.password.length < 8) {
      setError("Password must be at least 8 characters long");
      return false;
    }
    
    if (formData.password !== formData.confirmPassword) {
      setError("Passwords do not match");
      return false;
    }
    
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    const storedInviteToken = sessionStorage.getItem("invite_token");
    if (!storedInviteToken) {
      setError("Invite token not found. Please use the invitation link again.");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Submit signup data to backend
      const response = await fetch("http://localhost:8000/auth/accept-invite", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${storedInviteToken}`,
        },
        body: JSON.stringify({
          department: formData.department,
          full_name: formData.full_name,
          username: formData.username,
          password: formData.password,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || "Signup failed. Please try again.");
      }

      const userData = await response.json();
      console.log("Signup successful:", userData);

      // Clean up invite token
      sessionStorage.removeItem("invite_token");
      
      setSuccess(true);
      
      // Auto-login after successful signup
      setTimeout(() => {
        // Redirect to login or automatically log them in
        navigate("/feed");
      }, 2000);

    } catch (err: any) {
      console.error("Signup error:", err);
      setError(err.message || "An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <Layout>
        <div className="page-content container mx-auto px-4 py-8">
          <div className="max-w-md mx-auto">
            <Card className="project-detail-card">
              <CardContent className="p-6 text-center">
                <div className="text-green-400 text-6xl mb-4">✓</div>
                <h2 className="text-2xl font-['Orbitron'] text-[#00F5FF] mb-4">
                  Welcome to MESL!
                </h2>
                <p className="text-white/80 font-['Rajdhani'] mb-4">
                  Your account has been created successfully. You will be redirected to the feed shortly.
                </p>
                <Button 
                  onClick={() => navigate("/feed")}
                  className="bg-[var(--accent)] text-[var(--text)]"
                >
                  Go to Feed
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="page-content container mx-auto px-4 py-8">
        <div className="max-w-md mx-auto">
          <Button 
            onClick={() => navigate("/")} 
            variant="outline"
            className="mb-6 border-[var(--secondary)]/50 text-[var(--text)] hover:bg-[var(--secondary)]/20"
          >
            <IoArrowBack className="w-4 h-4 mr-2" />
            Back to Home
          </Button>

          <Card className="project-detail-card">
            <CardHeader>
              <CardTitle className="text-center">
                <div className="flex items-center justify-center space-x-2 mb-2">
                  <IoPersonAdd className="w-8 h-8 text-[#00F5FF]" />
                  <span className="text-2xl font-['Orbitron'] text-[#00F5FF]">
                    Complete Your Registration
                  </span>
                </div>
                <p className="text-white/60 font-['Rajdhani'] text-base font-normal">
                  Fill in your details to join the MESL community
                </p>
              </CardTitle>
            </CardHeader>
            
            <CardContent className="p-6">
              {error && (
                <Alert className="mb-6 border-red-500/30 bg-red-500/10">
                  <AlertDescription className="text-red-300">
                    {error}
                  </AlertDescription>
                </Alert>
              )}

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <Label htmlFor="full_name" className="text-white/80 font-['Rajdhani']">
                    Full Name *
                  </Label>
                  <Input
                    id="full_name"
                    name="full_name"
                    type="text"
                    value={formData.full_name}
                    onChange={handleInputChange}
                    placeholder="Enter your full name"
                    className="bg-[var(--background)] border-[var(--secondary)]/50 text-[var(--text)]"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="username" className="text-white/80 font-['Rajdhani']">
                    Username *
                  </Label>
                  <Input
                    id="username"
                    name="username"
                    type="text"
                    value={formData.username}
                    onChange={handleInputChange}
                    placeholder="Choose a username"
                    className="bg-[var(--background)] border-[var(--secondary)]/50 text-[var(--text)]"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="department" className="text-white/80 font-['Rajdhani']">
                    Department *
                  </Label>
                  <Input
                    id="department"
                    name="department"
                    type="text"
                    value={formData.department}
                    onChange={handleInputChange}
                    placeholder="Your department/field of study"
                    className="bg-[var(--background)] border-[var(--secondary)]/50 text-[var(--text)]"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="password" className="text-white/80 font-['Rajdhani']">
                    Password *
                  </Label>
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    placeholder="Create a secure password"
                    className="bg-[var(--background)] border-[var(--secondary)]/50 text-[var(--text)]"
                    required
                  />
                  <p className="text-xs text-white/50 mt-1">
                    Password must be at least 8 characters long
                  </p>
                </div>

                <div>
                  <Label htmlFor="confirmPassword" className="text-white/80 font-['Rajdhani']">
                    Confirm Password *
                  </Label>
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    placeholder="Confirm your password"
                    className="bg-[var(--background)] border-[var(--secondary)]/50 text-[var(--text)]"
                    required
                  />
                </div>

                <Button
                  type="submit"
                  disabled={loading}
                  className="w-full bg-[var(--accent)] text-[var(--text)] hover:bg-[var(--accent)]/80"
                >
                  {loading ? "Creating Account..." : "Complete Registration"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
};

export default Signup;
