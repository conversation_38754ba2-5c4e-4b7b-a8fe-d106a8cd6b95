import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { API_BASE_URL } from "@/lib/env";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Upload, Pause, Play, RotateCcw, X } from "lucide-react";
import { Upload as TusUpload } from "tus-js-client";

// Types for backend responses (adjust if your backend returns different shapes)
type DirectUploadResponse = {
  uploaded?: { url: string; type?: string; fileName?: string }[];
  // If mixed usage, backend may also return resumable items
  resumable?: { uploadId: string; uploadUrl: string; headers?: Record<string, string> }[];
  error?: string;
};

type ResumableInitResponse = {
  resumable?: { uploadId: string; uploadUrl: string; headers?: Record<string, string> }[];
  error?: string;
};

type ConfirmResponse = {
  publicUrl?: string;
  url?: string;
  error?: string;
};

type Props = {
  eventId: string;
  getAuthToken?: () => string | Promise<string>;
  onCompleted?: (items: UploadItem[]) => void;
};

type UploadStatus =
  | "queued"
  | "uploading"
  | "paused"
  | "completed"
  | "error";

type UploadItem = {
  id: string;
  file: File;
  kind: "image" | "video";
  size: number;
  smallUpload: boolean; // true for image and video <= 50MB
  status: UploadStatus;
  progress: number; // 0..100
  error?: string;
  previewUrl?: string; // for images
  publicUrl?: string; // final URL
  // resumable info
  uploadId?: string;
  uploadUrl?: string;
  tusUpload?: TusUpload;
};

const BYTES_50_MB = 50 * 1024 * 1024;

function formatBytes(bytes: number): string {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB", "TB"] as const;
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  const value = parseFloat((bytes / Math.pow(k, i)).toFixed(2));
  return `${value} ${sizes[i]}`;
}

async function defaultGetAuthToken(): Promise<string> {
  // Try to read from sessionStorage
  const token = sessionStorage.getItem("access_token") || "";
  return token;
}

export default function EventMediaUploader({ eventId, getAuthToken, onCompleted }: Props) {
  const [items, setItems] = useState<UploadItem[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const inputRef = useRef<HTMLInputElement | null>(null);

  const getToken = useCallback(async () => {
    const provided = getAuthToken ? await getAuthToken() : await defaultGetAuthToken();
    return provided || "";
  }, [getAuthToken]);

  const onFilesAdded = useCallback((files: FileList | File[]) => {
    const accepted: UploadItem[] = [];

    const fileArr = Array.from(files);
    for (const f of fileArr) {
      const type = (f.type || "").toLowerCase();
      const isImage = type.startsWith("image/");
      const isVideo = type.startsWith("video/");

      if (!isImage && !isVideo) {
        // Skip unsupported types; could also push an error item
        continue;
      }

      const small = isImage || (isVideo && f.size <= BYTES_50_MB);

      accepted.push({
        id: crypto.randomUUID(),
        file: f,
        kind: isImage ? "image" : "video",
        size: f.size,
        smallUpload: small,
        status: "queued",
        progress: 0,
        previewUrl: isImage ? URL.createObjectURL(f) : undefined,
      });
    }

    if (accepted.length === 0) return;

    setItems(prev => [...accepted, ...prev]);

    // Start uploads automatically
    for (const it of accepted) {
      if (it.smallUpload) {
        void directUpload(it.id);
      } else {
        void initResumable(it.id);
      }
    }
  }, []);

  const onDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      onFilesAdded(e.dataTransfer.files);
      e.dataTransfer.clearData();
    }
  }, [onFilesAdded]);

  const onDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(true);
  }, []);

  const onDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  }, []);

  const pickFiles = useCallback(() => {
    inputRef.current?.click();
  }, []);

  function updateItem(id: string, patch: Partial<UploadItem>) {
    setItems(prev => prev.map(it => (it.id === id ? { ...it, ...patch } : it)));
  }

  async function directUpload(id: string) {
    const item = items.find(i => i.id === id);
    if (!item) return;

    updateItem(id, { status: "uploading", progress: 0, error: undefined });

    try {
      const token = await getToken();
      const form = new FormData();
      form.append("files", item.file);

      // Use XMLHttpRequest for upload progress (fetch doesn't report progress reliably)
      const xhr = new XMLHttpRequest();
      const url = new URL(`/events/${eventId}/upload-media`, API_BASE_URL);
      xhr.open("POST", url.toString(), true);
      if (token) xhr.setRequestHeader("Authorization", `Bearer ${token}`);

      xhr.upload.onprogress = (evt) => {
        if (evt.lengthComputable) {
          const pct = Math.round((evt.loaded / evt.total) * 100);
          updateItem(id, { progress: pct });
        }
      };

      xhr.onerror = () => {
        updateItem(id, { status: "error", error: "Network error during upload" });
      };

      xhr.onload = () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const resp: DirectUploadResponse = JSON.parse(xhr.responseText || "{}");
            const url = resp.uploaded?.[0]?.url;
            updateItem(id, { status: "completed", progress: 100, publicUrl: url });
            if (onCompleted) onCompleted(items.map(i => (i.id === id ? { ...i, status: "completed", progress: 100, publicUrl: url } : i)));
          } catch {
            updateItem(id, { status: "completed", progress: 100 });
          }
        } else {
          updateItem(id, { status: "error", error: `Upload failed (${xhr.status})` });
        }
      };

      xhr.send(form);
    } catch (err: any) {
      updateItem(id, { status: "error", error: err?.message || String(err) });
    }
  }

  async function initResumable(id: string) {
    const item = items.find(i => i.id === id);
    if (!item) return;
    updateItem(id, { status: "uploading", progress: 0, error: undefined });

    try {
      const token = await getToken();
      const url = new URL(`/events/${eventId}/upload-media`, API_BASE_URL);
      const body = {
        resumable_init: [
          {
            fileName: item.file.name,
            size: item.file.size,
            contentType: item.file.type || "video/mp4",
          },
        ],
      };

      const resp = await fetch(url.toString(), {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          ...(token ? { Authorization: `Bearer ${token}` } : {}),
        },
        body: JSON.stringify(body),
      });

      if (!resp.ok) {
        const text = await resp.text();
        throw new Error(`Init failed: ${text || resp.statusText}`);
      }

      const data: ResumableInitResponse = await resp.json();
      const entry = data.resumable?.[0];
      if (!entry || !entry.uploadUrl) {
        throw new Error("Backend did not provide uploadUrl for resumable upload");
      }

      // Start TUS upload
      const tusUpload = new TusUpload(item.file, {
        uploadUrl: entry.uploadUrl, // Direct upload URL (server-provided)
        headers: entry.headers || {},
        metadata: {
          filename: item.file.name,
          filetype: item.file.type,
          eventId,
        },
        onError: (error) => {
          updateItem(id, { status: "error", error: error?.message || String(error) });
        },
        onProgress: (bytesUploaded, bytesTotal) => {
          const pct = Math.round((bytesUploaded / bytesTotal) * 100);
          updateItem(id, { progress: pct });
        },
        onSuccess: async () => {
          try {
            // Confirm upload with backend
            const confirmUrl = new URL(`/events/${eventId}/confirm-resumable-upload`, API_BASE_URL);
            const confirmResp = await fetch(confirmUrl.toString(), {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                ...(token ? { Authorization: `Bearer ${token}` } : {}),
              },
              body: JSON.stringify({ uploadId: entry.uploadId, fileName: item.file.name }),
            });

            if (!confirmResp.ok) {
              const txt = await confirmResp.text();
              throw new Error(`Confirm failed: ${txt || confirmResp.statusText}`);
            }

            const confirmData: ConfirmResponse = await confirmResp.json();
            const finalUrl = confirmData.publicUrl || confirmData.url;
            updateItem(id, { status: "completed", progress: 100, publicUrl: finalUrl });
            if (onCompleted) onCompleted(items.map(i => (i.id === id ? { ...i, status: "completed", progress: 100, publicUrl: finalUrl } : i)));
          } catch (e: any) {
            updateItem(id, { status: "error", error: e?.message || String(e) });
          }
        },
      });

      updateItem(id, { tusUpload, uploadId: entry.uploadId, uploadUrl: entry.uploadUrl });
      tusUpload.start();
    } catch (err: any) {
      updateItem(id, { status: "error", error: err?.message || String(err) });
    }
  }

  function pause(id: string) {
    const item = items.find(i => i.id === id);
    if (!item?.tusUpload) return;
    try {
      item.tusUpload.abort();
      updateItem(id, { status: "paused" });
    } catch (e: any) {
      updateItem(id, { status: "error", error: e?.message || String(e) });
    }
  }

  function resume(id: string) {
    const item = items.find(i => i.id === id);
    if (!item?.tusUpload) return;
    try {
      updateItem(id, { status: "uploading" });
      item.tusUpload.start();
    } catch (e: any) {
      updateItem(id, { status: "error", error: e?.message || String(e) });
    }
  }

  function retry(id: string) {
    const item = items.find(i => i.id === id);
    if (!item) return;
    if (item.smallUpload) {
      void directUpload(id);
    } else {
      // Recreate TUS upload from scratch
      updateItem(id, { tusUpload: undefined, progress: 0, error: undefined });
      void initResumable(id);
    }
  }

  function removeItem(id: string) {
    const item = items.find(i => i.id === id);
    try {
      item?.tusUpload?.abort();
    } catch {
      // ignore
    }
    setItems(prev => prev.filter(it => it.id !== id));
  }

  const totalCompleted = useMemo(() => items.filter(i => i.status === "completed").length, [items]);

  return (
    <Card className="project-detail-card">
      <CardHeader>
        <div className="flex items-center justify-between">
          <h3 className="font-['Orbitron'] text-xl font-bold text-[#00F5FF]">Manage Event Media</h3>
          <div className="text-sm text-[var(--text-secondary)]">{totalCompleted} completed</div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Dropzone */}
        <div
          onDrop={onDrop}
          onDragOver={onDragOver}
          onDragLeave={onDragLeave}
          className={`border-2 border-dashed rounded-md p-6 text-center cursor-pointer transition-colors ${
            dragActive ? "border-[var(--accent)] bg-[var(--accent)]/5" : "border-[var(--border)] hover:border-[var(--accent)]/60"
          }`}
          onClick={pickFiles}
        >
          <input
            ref={inputRef}
            type="file"
            multiple
            accept="image/*,video/*"
            className="hidden"
            onChange={(e) => {
              if (e.target.files) onFilesAdded(e.target.files);
              // reset input so selecting the same file again still triggers change
              if (inputRef.current) inputRef.current.value = "";
            }}
          />
          <div className="flex flex-col items-center gap-2">
            <Upload className="w-6 h-6 text-[#00F5FF]" />
            <div className="text-[var(--text)]">Drag & drop images/videos here, or click to browse</div>
            <div className="text-xs text-[var(--text-secondary)]">Accepted: image/*, video/* • Images & small videos upload directly • Large videos use resumable upload</div>
          </div>
        </div>

        {/* Items List */}
        {items.length > 0 && (
          <div className="mt-6 space-y-3">
            {items.map((it) => (
              <div key={it.id} className="border border-[var(--border)] rounded-md p-3 grid grid-cols-1 md:grid-cols-12 gap-3 items-center">
                {/* Preview / Info */}
                <div className="md:col-span-6 flex items-center gap-3">
                  {it.kind === "image" ? (
                    <img src={it.previewUrl} alt={it.file.name} className="w-16 h-16 object-cover rounded border border-[var(--border)]" />
                  ) : (
                    <div className="w-16 h-16 rounded bg-[var(--background)]/50 border border-[var(--border)] flex items-center justify-center text-xs text-[var(--text-secondary)]">VIDEO</div>
                  )}
                  <div className="min-w-0">
                    <div className="truncate font-medium text-[var(--text)]">{it.file.name}</div>
                    <div className="text-xs text-[var(--text-secondary)]">{formatBytes(it.size)} • {it.kind.toUpperCase()}</div>
                  </div>
                </div>

                {/* Progress */}
                <div className="md:col-span-4">
                  <div className="flex items-center justify-between text-xs mb-1">
                    <span className="text-[var(--text-secondary)]">{it.status === "uploading" ? "Uploading" : it.status === "paused" ? "Paused" : it.status === "completed" ? "Completed" : it.status === "error" ? "Error" : "Queued"}</span>
                    <span className="text-[var(--text-secondary)]">{it.progress}%</span>
                  </div>
                  <Progress value={it.progress} />
                  {it.publicUrl && (
                    <div className="text-xs mt-1 truncate text-[#00F5FF]">{it.publicUrl}</div>
                  )}
                  {it.status === "error" && it.error && (
                    <div className="text-xs mt-1 text-red-400">{it.error}</div>
                  )}
                </div>

                {/* Actions */}
                <div className="md:col-span-2 flex items-center justify-end gap-2">
                  {it.smallUpload ? null : (
                    it.status === "uploading" ? (
                      <Button size="sm" variant="outline" onClick={() => pause(it.id)}>
                        <Pause className="w-4 h-4 mr-1" /> Pause
                      </Button>
                    ) : it.status === "paused" ? (
                      <Button size="sm" variant="outline" onClick={() => resume(it.id)}>
                        <Play className="w-4 h-4 mr-1" /> Resume
                      </Button>
                    ) : null
                  )}

                  {it.status === "error" && (
                    <Button size="sm" variant="outline" onClick={() => retry(it.id)}>
                      <RotateCcw className="w-4 h-4 mr-1" /> Retry
                    </Button>
                  )}

                  {it.status !== "uploading" && (
                    <Button size="sm" variant="outline" className="border-red-500 text-red-500 hover:bg-red-500 hover:text-white" onClick={() => removeItem(it.id)}>
                      <X className="w-4 h-4 mr-1" /> Remove
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}