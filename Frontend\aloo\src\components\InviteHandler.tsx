import React, { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";

/**
 * Component to handle invite links from email
 * Processes URLs like: http://SITEURL/#access_token=<token>&expires_at=<time>&expires_in=<seconds>&refresh_token=<token>&token_type=bearer&type=invite
 */
const InviteHandler = () => {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Check if this is an invite link
    const hash = location.hash;
    if (!hash || !hash.includes("type=invite")) {
      return;
    }

    try {
      // Parse the hash parameters
      const params = new URLSearchParams(hash.substring(1)); // Remove the # symbol

      const accessToken = params.get("access_token");
      const expiresAt = params.get("expires_at");
      const expiresIn = params.get("expires_in");
      const refreshToken = params.get("refresh_token");
      const tokenType = params.get("token_type");
      const type = params.get("type");

      console.log("Processing invite link:", {
        accessToken: accessToken?.substring(0, 20) + "...",
        type,
        expiresAt,
      });

      // Validate that this is an invite link
      if (type !== "invite" || !accessToken) {
        console.error("Invalid invite link parameters", {
          type,
          hasToken: !!accessToken,
        });
        return;
      }

      // Store the invite tokens temporarily
      if (accessToken) {
        sessionStorage.setItem("invite_token", accessToken);
        console.log("Stored invite token");
      }
      if (refreshToken) {
        sessionStorage.setItem("invite_refresh_token", refreshToken);
      }
      if (expiresAt) {
        sessionStorage.setItem("invite_expires_at", expiresAt);
      }

      console.log("Invite link processed, redirecting to signup...");

      // Redirect to signup page with the token parameters
      const signupUrl = `/signup?access_token=${accessToken}&expires_at=${expiresAt}&expires_in=${expiresIn}&refresh_token=${refreshToken}&token_type=${tokenType}&type=${type}`;
      navigate(signupUrl, { replace: true });
    } catch (error) {
      console.error("Error processing invite link:", error);
    }
  }, [location.hash, navigate]);

  // This component doesn't render anything
  return null;
};

export default InviteHandler;
