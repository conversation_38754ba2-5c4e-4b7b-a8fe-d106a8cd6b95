import React from "react";
import { Link } from "react-router-dom";
import {
  IoMail,
  IoLocation,
  IoLogoFacebook,
  IoLogoLinkedin,
} from "react-icons/io5";

const Footer = () => {
  return (
    <footer className="bg-gradient-secondary border-t border-glow relative">
      <div className=" mx-auto px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Logo and Institution Logos */}
          <div className="space-y-6">
            {/* MESL Logo and Name */}
            <div className="flex items-center space-x-4">
              <img
                src="/images/Footer logos/mesl-logo.png"
                alt="MESL Logo"
                className="w-16 h-16 object-contain"
              />
              <div>
                <h1 className="font-['Orbitron'] text-lg sm:text-xl lg:text-2xl font-extrabold text-[--text]">
                  Mechatronics & Embedded System Lab
                </h1>
                <p className="text-[--text-secondary] font-['Rajdhani'] text-md sm:text-lg lg:text-xl font-bold">
                  Transforming Ideas into Reality
                </p>
              </div>
            </div>

            {/* Institution Logos */}
            <div className="flex items-center space-x-4">
              <img
                src="https://upload.wikimedia.org/wikipedia/commons/1/1c/Government_of_Bangladesh_Logo_%28unofficial_and_fictional_logo%29.png"
                alt="Government of Bangladesh"
                className="w-12 h-12 object-contain"
              />
              <img
                src="/images/Footer logos/MEC.png"
                alt="MEC Logo"
                className="w-12 h-12 object-contain"
              />
              <img
                src="/images/Footer logos/DuLogo-removebg-preview.png"
                alt="University Logo"
                className="w-12 h-12 object-contain"
              />
            </div>
          </div>

          {/* Links and Contact */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Navigation Links */}
            <div>
              <h4 className="font-['Orbitron'] text-lg font-semibold text-[--text] mb-4">
                Navigation
              </h4>
              <ul className="space-y-2">
                <li>
                  <Link
                    to="/"
                    className="text-[--text-secondary] hover:text-[#00F5FF] transition-colors font-['Rajdhani']"
                  >
                    Home
                  </Link>
                </li>
                <li>
                  <Link
                    to="/projects"
                    className="text-[--text-secondary] hover:text-[#00F5FF] transition-colors font-['Rajdhani']"
                  >
                    Projects
                  </Link>
                </li>
                <li>
                  <Link
                    to="/feed"
                    className="text-[--text-secondary] hover:text-[#00F5FF] transition-colors font-['Rajdhani']"
                  >
                    Feed
                  </Link>
                </li>
                <li>
                  <Link
                    to="/gallery"
                    className="text-[--text-secondary] hover:text-[#00F5FF] transition-colors font-['Rajdhani']"
                  >
                    Gallery
                  </Link>
                </li>
              </ul>
            </div>

            {/* Resources */}
            <div>
              <h4 className="font-['Orbitron'] text-lg font-semibold text-[--text] mb-4">
                Resources
              </h4>
              <ul className="space-y-2">
                <li>
                  <a
                    href="#"
                    className="text-[--text-secondary] hover:text-[#00F5FF] transition-colors font-['Rajdhani']"
                  >
                    Documentation
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-[--text-secondary] hover:text-[#00F5FF] transition-colors font-['Rajdhani']"
                  >
                    Research Papers
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-[--text-secondary] hover:text-[#00F5FF] transition-colors font-['Rajdhani']"
                  >
                    Lab Tools
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-[--text-secondary] hover:text-[#00F5FF] transition-colors font-['Rajdhani']"
                  >
                    Equipment
                  </a>
                </li>
              </ul>
            </div>

            {/* Contact */}
            <div>
              <h4 className="font-['Orbitron'] text-lg font-semibold text-[--text] mb-4">
                Contact Us
              </h4>
              <ul className="space-y-3">
                <li>
                  <a
                    href="mailto:<EMAIL>"
                    className="flex items-center space-x-2 text-[--text-secondary] hover:text-[#00F5FF] transition-colors font-['Rajdhani']"
                  >
                    <IoMail className="w-4 h-4" />
                    <span><EMAIL></span>
                  </a>
                </li>
                <li>
                  <a
                    href="https://maps.app.goo.gl/nnhJLWKL8MLzzehV6"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-start space-x-2 text-[--text-secondary] hover:text-[#00F5FF] transition-colors font-['Rajdhani']"
                  >
                    <IoLocation className="w-4 h-4 mt-1 flex-shrink-0" />
                    <span>
                      Mymensingh Engineering College, Mymensingh, Bangladesh
                    </span>
                  </a>
                </li>
                <li>
                  <a
                    href="https://www.facebook.com/MESLMEC"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center space-x-2 text-[--text-secondary] hover:text-[#00F5FF] transition-colors font-['Rajdhani']"
                  >
                    <IoLogoFacebook className="w-4 h-4" />
                    <span>Mechatronics & Embedded System Lab</span>
                  </a>
                </li>
                <li>
                  <a
                    href="https://www.linkedin.com/company/mechatronics-embedded-system-lab/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center space-x-2 text-[--text-secondary] hover:text-[#00F5FF] transition-colors font-['Rajdhani']"
                  >
                    <IoLogoLinkedin className="w-4 h-4" />
                    <span>Mechatronics & Embedded System Lab</span>
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="mt-8 pt-6 border-t border-[--text-secondary]/20 text-center">
          <p className="text-[--text-secondary] font-['Rajdhani']">
            &copy; 2025 MESL. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
