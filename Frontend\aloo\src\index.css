@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Bright/Light Professional Tech Color Variables */
  --primary: #fdfafa;
  --secondary: #0078d4;
  --accent: #0466c8;

  --background: #f5f5f5;
  --text: #3a3b3b;
  --text-secondary: #262729;

  --border: #e9ecef;
  --card-bg: rgba(255, 255, 255, 0.95);

  --overlay: rgba(255, 255, 255, 0.5); /* More transparent for blur effect */

  --shadow: 0 4px 6px rgba(0, 0, 0, 0.05);

  --glow: 0 0 8px var(--secondary);
  --glow-strong: 0 0 15px var(--secondary), 0 0 30px rgba(0, 120, 212, 0.3);

  /* Typography */
  --font-heading: "Orbitron", sans-serif;
  --font-body: "Rajdhani", sans-serif;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;

  /* Layout */
  --navbar-height: 70px;
  --container-width: 1400px;
  --border-radius: 4px;
  --card-radius: 8px;
}

/* Dark theme class to be toggled */
:root.dark-theme {
  --primary: #001233;
  --secondary: #00f5ff;
  --accent: #0466c8;
  --background: #1a1a2e;
  --text: #ffffff;
  --text-secondary: #a0a0b8;
  --border: #2a2a4e;
  --card-bg: rgba(15, 15, 35, 0.6);
  --overlay: rgba(0, 0, 0, 0.7);
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --glow: 0 0 8px var(--secondary);
  --glow-strong: 0 0 15px var(--secondary), 0 0 30px rgba(0, 245, 255, 0.4);
}
/* Theme Toggle Button */
.theme-toggle {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  background: var(--card-bg);
  border: 1px solid var(--border);
  color: var(--text);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 999;
  box-shadow: var(--shadow);
}

.theme-toggle:hover {
  border-color: var(--secondary);
  color: var(--secondary);
  box-shadow: var(--glow);
  transform: scale(1.05);
}

.theme-toggle-icon {
  font-size: 1.5rem;
}

/* Responsive adjustment for mobile */
@media (max-width: 768px) {
  .theme-toggle {
    bottom: 1.5rem;
    right: 1.5rem;
    width: 45px;
    height: 45px;
  }
}

/* Define both themes */

/* Reset & Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body {
  height: 100%;
}
html {
  scroll-behavior: smooth;
  /* font-size: 62.5%; */
  overflow-x: hidden;
}

body {
  font-family: var(--font-body);
  background-color: var(--background);
  color: var(--text);
  line-height: 1.6;
  overflow-x: hidden;
  position: relative;
}

/* Interactive background grid - now created programmatically */
#interactive-grid {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  display: grid;
  grid-template-columns: repeat(20, 1fr);
  grid-template-rows: repeat(20, 1fr);
  pointer-events: none;
}

/* Click ripple effect */
.bg-cell.clicked {
  animation: ripple 0.6s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 120, 212, 0.7);
  }
  70% {
    transform: scale(1.1);
    box-shadow: 0 0 0 10px rgba(0, 120, 212, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 120, 212, 0);
  }
}

:root.dark-theme .bg-cell.clicked {
  animation: ripple-dark 0.6s ease-out;
}

@keyframes ripple-dark {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 245, 255, 0.7);
  }
  70% {
    transform: scale(1.1);
    box-shadow: 0 0 0 10px rgba(0, 245, 255, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 245, 255, 0);
  }
}

/* Additional background effects */
.interactive-background::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
      circle at 30% 20%,
      rgba(0, 120, 212, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 70% 80%,
      rgba(0, 120, 212, 0.1) 0%,
      transparent 50%
    );
  pointer-events: none;
}

:root.dark-theme .interactive-background::before {
  background: radial-gradient(
      circle at 30% 20%,
      rgba(0, 245, 255, 0.15) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 70% 80%,
      rgba(4, 102, 200, 0.15) 0%,
      transparent 50%
    );
}

/* Subtle floating particles */
.floating-particle {
  position: fixed;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background: rgba(0, 120, 212, 0.4);
  pointer-events: none;
  z-index: -1;
  animation: float 15s infinite linear;
  box-shadow: 0 0 5px rgba(0, 120, 212, 0.3);
}

:root.dark-theme .floating-particle {
  background: rgba(0, 245, 255, 0.4);
  box-shadow: 0 0 5px rgba(0, 245, 255, 0.3);
}

/* Enhanced dropdown styling */
select {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.7rem center;
  background-size: 1.2em;
  padding-right: 2.5rem;
}

select:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--secondary);
}

select option {
  padding: 0.5rem;
  background: var(--card-bg);
  color: var(--text);
  border: none;
}

select option:hover,
select option:focus {
  background: var(--secondary);
  color: var(--text);
}

@keyframes float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-heading);
  font-weight: 600;
  letter-spacing: 1px;
  margin-bottom: var(--spacing-md);
  text-transform: uppercase;
}

a {
  color: var(--text);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--secondary);
}

img {
  max-width: 100%;
  height: auto;
}

button {
  font-family: var(--font-heading);
  cursor: pointer;
  border: none;
  outline: none;
  font-weight: 500;
  letter-spacing: 1px;
  transition: all 0.3s ease;
}

.container {
  width: 100%;
  max-width: var(--container-width);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.section-header h2 {
  font-size: 2rem;
  margin-bottom: var(--spacing-sm);
}

.section-header p {
  color: var(--text-secondary);
  font-size: 1.1rem;
}

/* Utility Classes */
.neon-text {
  color: var(--secondary);
  text-shadow: 0 0 5px var(--secondary), 0 0 10px rgba(0, 245, 255, 0.5);
}

.glitch {
  /* position: relative; */
  font-size: 3rem;
  /* text-transform: uppercase;
  color: var(--text);
  animation: glitch 2s infinite; */
}

/* .glitch::before,
.glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch::before {
  left: 2px;
  text-shadow: -2px 0 var(--secondary);
  clip: rect(44px, 450px, 56px, 0);
  animation: glitch-effect 5s infinite linear alternate-reverse;
}

.glitch::after {
  left: -2px;
  text-shadow: 2px 0 var(--accent);
  clip: rect(24px, 450px, 36px, 0);
  animation: glitch-effect 4s infinite linear alternate-reverse;
}

@keyframes glitch-effect {
  0% {
    clip: rect(24px, 450px, 36px, 0);
  }
  20% {
    clip: rect(12px, 450px, 64px, 0);
  }
  40% {
    clip: rect(48px, 450px, 92px, 0);
  }
  60% {
    clip: rect(32px, 450px, 78px, 0);
  }
  80% {
    clip: rect(84px, 450px, 112px, 0);
  }
  100% {
    clip: rect(56px, 450px, 134px, 0);
  }
} */

.btn-primary,
.btn-secondary {
  padding: 0.8rem 2rem;
  border-radius: var(--border-radius);
  font-size: 1rem;
  text-transform: uppercase;
  font-weight: 600;
  border: 2px solid transparent;
}

.btn-primary {
  background: var(--secondary);
  color: var(--primary);
  box-shadow: var(--glow);
}

.btn-primary:hover {
  background: transparent;
  color: var(--secondary);
  border-color: var(--secondary);
  box-shadow: var(--glow-strong);
}

.btn-secondary {
  background: transparent;
  color: var(--secondary);
  border-color: var(--secondary);
}

.btn-secondary:hover {
  background: rgba(0, 245, 255, 0.1);
  box-shadow: var(--glow);
}

.btn-block {
  display: block;
  width: 100%;
}

.btn-rsvp {
  background: transparent;
  color: var(--secondary);
  border: 1px solid var(--secondary);
  padding: 0.5rem 1.5rem;
  border-radius: var(--border-radius);
}

.btn-rsvp:hover {
  background: var(--secondary);
  color: var(--primary);
  box-shadow: var(--glow);
}

.btn-view {
  background: transparent;
  color: var(--secondary);
  border: 1px solid var(--border);
  padding: 0.4rem 1rem;
  border-radius: var(--border-radius);
  font-size: 0.9rem;
}

.btn-view:hover {
  border-color: var(--secondary);
  box-shadow: var(--glow);
}

.btn-login,
.btn-signup {
  background: transparent;
  color: var(--text);
  padding: 0.5rem 1.2rem;
  margin-left: var(--spacing-md);
  border-radius: var(--border-radius);
  font-size: 0.9rem;
}

.btn-login {
  border: 1px solid var(--border);
}

.btn-login:hover {
  border-color: var(--secondary);
  color: var(--secondary);
}

.btn-signup {
  background: var(--accent);
  color: white;
}

.btn-signup:hover {
  background: rgba(4, 102, 200, 0.8);
}

/* Navbar */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: var(--navbar-height);
  background: var(--card-bg);
  backdrop-filter: blur(10px);
  z-index: 1000;
  border-bottom: 1px solid var(--border);
  color: var(--text);
}

/* Body styles */
body {
  font-family: var(--font-body);
  background-color: var(--background);
  color: var(--text);
  line-height: 1.6;
  overflow-x: hidden;
  /* Background image removed in light mode by default */
}

/* Dark theme body background */
:root.dark-theme body {
  background-image: radial-gradient(
      circle at top right,
      rgba(6, 6, 45, 0.5),
      transparent 70%
    ),
    radial-gradient(
      circle at bottom left,
      rgba(4, 102, 200, 0.2),
      transparent 70%
    ),
    linear-gradient(to bottom, var(--background), var(--primary));
  background-attachment: fixed;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-lg);
  /* height: 100%; */
}

.navbar-logo a {
  font-family: var(--font-heading);
  font-size: 1.5rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.navbar-links {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.navbar-links a {
  font-family: var(--font-heading);
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
}

.navbar-links a::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--secondary);
  transition: width 0.3s ease;
}

.navbar-links a:hover::after,
.navbar-links a.active::after {
  width: 100%;
  box-shadow: var(--glow);
}

.navbar-links a.active {
  color: var(--secondary);
}

.navbar-toggle {
  display: none;
  flex-direction: column;
  gap: 6px;
  cursor: pointer;
}

.navbar-toggle span {
  display: block;
  width: 28px;
  height: 2px;
  background: var(--text);
  transition: all 0.3s ease;
}

/* Page content padding for fixed navbar */
.page-content {
  padding-top: calc(var(--navbar-height) + var(--spacing-lg));
}

/* Project detail page enhancements */
.project-detail-card {
  background: var(--card-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border);
  border-radius: var(--card-radius);
  transition: all 0.3s ease;
  color: var(--text);
}

.project-detail-card:hover {
  border-color: var(--secondary);
  box-shadow: var(--glow);
}

/* Ensure proper text color inheritance */
.project-detail-card h1,
.project-detail-card h2,
.project-detail-card h3,
.project-detail-card h4,
.project-detail-card h5,
.project-detail-card h6 {
  color: var(--secondary);
}

.project-detail-card p,
.project-detail-card span,
.project-detail-card div {
  color: var(--text);
}

/* Light mode text adjustments */
:root:not(.dark-theme) .project-detail-card {
  color: var(--text);
}

:root:not(.dark-theme) .project-detail-card h1,
:root:not(.dark-theme) .project-detail-card h2,
:root:not(.dark-theme) .project-detail-card h3 {
  color: var(--secondary);
}

/* Featured Projects and Events sections - seamless background */
.featured-projects-section,
.upcoming-events-section {
  background: transparent;
  position: relative;
}

/* Subtle section dividers without background interference */
.featured-projects-section::before,
.upcoming-events-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    var(--border) 50%,
    transparent 100%
  );
  opacity: 0.3;
}

/* Line clamp utility for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Hero Section */
.hero {
  padding-top: calc(var(--navbar-height) + var(--spacing-xl));
  padding-bottom: var(--spacing-xl);
  min-height: 70vh;
  display: flex;
  align-items: center;
  position: relative;
  background: none;
}

.hero-grid {
  display: grid;
  grid-template-columns: 1.4fr 1fr;
  align-items: center;
  position: relative;
  min-height: 400px;
  overflow: visible; /* Allow logo to overflow */
}

.hero-content {
  z-index: 2;
  text-align: left;
  padding-left: var(--spacing-xl);
  padding-right: var(--spacing-lg);
}

.hero-content h1 {
  font-size: 3.2rem;
  margin-bottom: var(--spacing-md);
  line-height: 1.05;
}

.hero-content p {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-lg);
  color: var(--text-secondary);
}

.hero-buttons {
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-start;
}
/* Larger buttons specifically in hero */
.hero .btn-primary, .hero .btn-secondary {
  padding: 1rem 2.4rem;
  font-size: 1.05rem;
}

/* Trusted By */
.hero-trusted{display:flex;align-items:center;gap:.75rem;margin-top:.85rem}
.hero-trusted .label{font-size:.85rem;color:var(--text-secondary)}
.hero-trusted .logos{display:flex;gap:.75rem;align-items:center}
.hero-trusted .logos img{height:28px;opacity:.7;filter:grayscale(100%);transition:opacity .2s ease}
.hero-trusted .logos img:hover{opacity:1}


.hero-logo-wrap {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  overflow: visible;
  height: 100%;
}

/* Subtle radial glow behind the logo */
.hero-logo-wrap::before {
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: min(80%, 640px);
  aspect-ratio: 1 / 1;
  background: radial-gradient(circle, rgba(0, 245, 255, 0.14) 0%, rgba(0, 245, 255, 0.0) 60%);
  filter: blur(2px);
  pointer-events: none;
  z-index: 0;
}

.hero-logo {
  width: 100%;
  max-width: 100%;
  height: auto;
  max-height: 80vh;
  object-fit: cover;
  opacity: 0.3;
  position: static;
  pointer-events: none;
  user-select: none;
  z-index: 1;
}

@media (max-width: 1200px) {
  .hero-logo {
    width: 100%;
    height: auto;
    max-height: 70vh;
  }
}

@media (max-width: 900px) {
  .hero-grid {
    grid-template-columns: 1fr;
    min-height: 250px;
  }
  .hero-content {
    text-align: center;
    padding-left: 0;
    padding-right: 0;
  }
  .hero-buttons {
    justify-content: center;
  }
  .hero-logo-wrap {
    justify-content: center;
  }
  .hero-logo {
    position: static;
    width: 100%;
    max-width: 600px;
    height: auto;
    margin: 0 auto 1.5rem auto;
    opacity: 0.2;
    transform: none;
    right: 0;
    object-fit: cover;
  }
}

/* Leadership Messages Section - Flex layout */
.leadership-message {
  display: flex;
  align-items: stretch;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
  min-height: 170px;
  opacity: 0;
  transform: translateY(40px);
  transition: transform 1s cubic-bezier(0.23, 1, 0.32, 1), opacity 1s;
  will-change: transform, opacity;
}

/* Left-aligned: avatar left, message right */
.leadership-message.left {
  flex-direction: row;
}
.leadership-message.left .message-avatar {
  order: 0;
  margin-right: 0.2rem;
  align-self: center;
}
.leadership-message.left .message-bubble {
  order: 1;
  flex: 1 1 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-self: stretch;
}

/* Right-aligned: avatar right, message left */
.leadership-message.right {
  flex-direction: row-reverse;
}
.leadership-message.right .message-avatar {
  order: 0;
  margin-left: 0.2rem;
  align-self: center;
}
.leadership-message.right .message-bubble {
  order: 1;
  flex: 1 1 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-self: stretch;
}

/* Avatar Sizing */
.message-avatar {
  width: 120px;
  height: 150px;
  border-radius: 16px;
  overflow: hidden;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  background: #fff;
  display: block;
  margin: 0;
}

.message-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 16px;
  display: block;
}

/* Animation for leadership messages */
.leadership-message.left {
  transform: translateX(-80px);
}
.leadership-message.right {
  transform: translateX(80px);
}
.leadership-message.show {
  opacity: 1;
  transform: translateX(0) translateY(0);
}

/* Responsive: stack vertically on small screens */
@media (max-width: 700px) {
  .leadership-message,
  .leadership-message.left,
  .leadership-message.right {
    flex-direction: column !important;
    align-items: center;
    min-height: unset;
  }
  .message-avatar,
  .message-bubble {
    width: 100% !important;
    margin: 0 !important;
    align-self: center !important;
  }
  .message-bubble {
    text-align: center;
  }
}

/* Featured Projects */

.home-project {
  opacity: 0;
  transition: transform 1.4s ease, opacity 1.4s ease;
  will-change: transform, opacity;
}

.home-project.hidden.slide-in-left {
  transform: translateX(-100px);
}

.home-project.hidden.slide-in-right {
  transform: translateX(100px);
}

.home-project.show {
  opacity: 1;
  transform: translateX(0);
}

.featured-projects {
  padding: var(--spacing-xl) 0;
}

.project-grid {
  display: grid;
  grid-template-columns: repeat(
    auto-fill,
    minmax(350px, 1fr)
  ); /* Match projects.html */
  gap: var(--spacing-lg);
}

.project-card {
  border-radius: var(--card-radius);
  background: var(--card-bg);
  backdrop-filter: blur(15px);
  overflow: hidden;
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  min-height: 350px !important;
  height: auto !important;
  border: 1px solid var(--border);
  display: flex;
  flex-direction: column;
}

/* 3. Match the image height */
.project-image {
  width: 100%;
  height: 180px;
  overflow: hidden;
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-md);
  border: 1px solid var(--border);
}

.project-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
  border-radius: var(--border-radius);
  display: block;
}

/* 4. Ensure .project-card-inner matches projects.html */
.project-card-inner {
  padding: var(--spacing-lg);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.project-header h3 {
  margin-bottom: var(--spacing-sm);
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-md);
}

.project-tech span {
  background: rgba(4, 102, 200, 0.2);
  padding: 2px 8px;
  border-radius: 20px;
  font-size: 0.8rem;
  color: var(--text-secondary);
  border: 1px solid rgba(4, 102, 200, 0.4);
}

.project-content {
  margin-bottom: var(--spacing-md);
  flex-grow: 1;
}

.project-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.project-team {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.view-all {
  margin-top: var(--spacing-xl);
  text-align: center;
}

/* Events Section */
/* Initial hidden state for rising effect */
.event-card.hidden-rise {
  opacity: 0;
  transform: translateY(80px);
  transition: transform 1.5s ease, opacity 1.5s ease;
}

/* Visible state */
.event-card.show-rise {
  opacity: 1;
  transform: translateY(0);
}

.events {
  padding: var(--spacing-xl) 0;
}

.event-carousel {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--spacing-lg);
}

.event-card {
  background: var(--card-bg);
  border-radius: var(--card-radius);
  padding: var(--spacing-md);
  border: 1px solid var(--border);
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  gap: var(--spacing-md);
  align-items: center;
}
.event-body {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.event-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--glow);
  border-color: var(--secondary);
}

.event-date {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm);
  margin-right: var(--spacing-md);
  border-right: 1px solid var(--border);
}

.event-date .day {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--secondary);
}

.event-date .month {
  font-size: 1rem;
  color: var(--text-secondary);
}

.event-details {
  flex-grow: 1;
}

.event-details h3 {
  margin-bottom: var(--spacing-xs);
  font-size: 1.2rem;
}

.event-details p {
  margin-bottom: var(--spacing-sm);
  font-size: 0.95rem;
  color: var(--text-secondary);
}

.event-meta {
  display: flex;
  flex-direction: column;
  font-size: 0.85rem;
}

.event-cta {
  display: flex;
  align-items: center;
  /* justify-content: end; */
  margin-left: var(--spacing-md);
  margin-top: var(--spacing-md);
}

/* Footer */
footer {
  background: var(--primary);
  padding: var(--spacing-xl) 0 var(--spacing-md);
  border-top: 1px solid var(--border);
  margin-top: var(--spacing-xl);
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  justify-content: space-around;
  /* flex-wrap: wrap; */
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  margin-left: var(--spacing-xl);
}

.footer-logo {
  font-family: var(--font-heading);
  font-size: 1.8rem;
  font-weight: 700;
}
.all-logo {
  margin: 1rem 4.2rem;
  align-items: center;
  display: flex;
  gap: var(--spacing-xl); /* Debugging */
}

.all-logo img.akar {
  width: 48px;
  height: 48px;
  object-fit: contain;
}
/* #bd-logo {
  padding: -50px;
  margin: 0;
} */
.mesl-part {
  display: flex;
  /* justify-content: space-around; */
  align-items: center;
  gap: var(--spacing-sm);
}
.mesl-logo {
  width: 90px;
  height: 90px;
  /* align-self: top; */
  margin-bottom: var(--spacing-md);
}
.mesl-footer-name {
  font-size: 28px;
  align-self: center;
  font-weight: 700;
  margin-bottom: var(--spacing-sm);
  text-transform: uppercase;
}
.footer-name p {
  font-size: 1rem;
  font-weight: 400;
  text-decoration: none;
}
.footer-links {
  display: flex;
  gap: var(--spacing-xl);
  flex-wrap: wrap;
}

.footer-col h4 {
  margin-bottom: var(--spacing-md);
  font-size: 1.1rem;
}

.footer-col ul {
  list-style: none;
}

.footer-col li {
  margin-bottom: var(--spacing-xs);
}

.footer-col a {
  color: var(--text-secondary);
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.footer-col a:hover {
  color: var(--secondary);
}

.footer-bottom {
  text-align: center;
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border);
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 99999;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease, backdrop-filter 0.3s ease;
  backdrop-filter: blur(0px);
  cursor: pointer;
  /* Ensure modal creates its own stacking context */
  isolation: isolate;
  /* Ensure it's not affected by transforms */
  transform: translateZ(0);
}

.modal.active {
  opacity: 1;
  visibility: visible;
  backdrop-filter: blur(8px);
}

:root.dark-theme .modal {
  background: rgba(0, 0, 0, 0.7);
}

:root.dark-theme .modal.active {
  backdrop-filter: blur(12px);
}

/* Ensure modal is always on top and properly positioned */
.modal {
  pointer-events: auto;
}

.modal.active {
  pointer-events: auto;
}

/* Prevent any interference from other elements */
.modal * {
  box-sizing: border-box;
}

/* Modal content should not close on click */
.modal-content {
  background: var(--card-bg);
  border-radius: var(--card-radius);
  padding: var(--spacing-xl);
  width: 90%;
  max-width: 450px;
  max-height: 90vh;
  position: relative;
  backdrop-filter: blur(15px);
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
  transform: translateY(20px) scale(0.95);
  transition: transform 0.3s ease;
  overflow-y: auto;
  margin: 1rem;
  cursor: default;
  z-index: 100000;
  /* Ensure proper stacking context */
  isolation: isolate;
}

.modal.active .modal-content {
  transform: translateY(0) scale(1);
}

/* Ensure modal is properly centered on all screen sizes */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    max-width: none;
    margin: 0.5rem;
    padding: var(--spacing-lg);
  }
}

@media (max-height: 600px) {
  .modal-content {
    max-height: 95vh;
    padding: var(--spacing-md);
  }
}

/* Add a subtle glow effect to the modal in dark mode */
:root.dark-theme .modal-content {
  box-shadow: 0 0 20px rgba(0, 245, 255, 0.1);
  border-color: rgba(0, 245, 255, 0.2);
}

.close-modal {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  font-size: 1.5rem;
  cursor: pointer;
  background: none;
  border: none;
  color: var(--text);
  transition: color 0.3s ease;
}

.close-modal:hover {
  color: var(--secondary);
}

.modal h2 {
  text-align: center;
  margin-bottom: var(--spacing-lg);
  font-size: 1.8rem;
  color: var(--text);
}

.form-group {
  margin-bottom: var(--spacing-md);
  width: 100%;
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: var(--text);
  font-size: 0.9rem;
}

/* Ensure form takes full width */
.modal form {
  width: 100%;
}

/* Form note styling */
.form-note {
  text-align: center;
  margin-top: var(--spacing-md);
  font-size: 0.9rem;
  color: var(--text-secondary);
  width: 100%;
}

/* Enhanced form input styles for the login modal */
.form-group .relative {
  position: relative;
}

.form-group input[type="email"],
.form-group input[type="text"] {
  padding-left: 2.5rem;
  padding-right: 0.75rem;
}

.form-group input[type="password"] {
  padding-left: 2.5rem;
  padding-right: 3rem;
}

/* Input error state */
.form-group input.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 1px #ef4444;
}

.form-group input.error:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

/* Error states */
.error-message {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
}

/* Icon positioning in inputs */
.form-group .relative svg {
  pointer-events: none;
  color: var(--text-secondary);
}

/* Password toggle button */
.form-group .relative button[type="button"] {
  background: none;
  border: none;
  padding: 0.25rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 2rem;
  min-height: 2rem;
  border-radius: 0.25rem;
}

.form-group .relative button[type="button"]:hover {
  color: var(--text);
  background: rgba(0, 0, 0, 0.05);
}

:root.dark-theme .form-group .relative button[type="button"]:hover {
  background: rgba(255, 255, 255, 0.05);
}

.form-group .relative button[type="button"]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.form-group .relative button[type="button"]:focus {
  outline: 2px solid var(--secondary);
  outline-offset: 2px;
}

/* Link button styling */
.link-button {
  background: none;
  border: none;
  color: var(--secondary);
  cursor: pointer;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
}

.link-button:hover {
  text-decoration: underline;
  color: var(--secondary);
  opacity: 0.8;
}

/* Loading spinner */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Flex utilities for modal */
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.gap-2 {
  gap: 0.5rem;
}

.form-group input,
.form-group select,
textarea,
.post-form textarea,
input[type="text"],
input[type="email"],
input[type="password"],
select {
  width: 100%;
  padding: 0.8rem;
  background: var(--primary); /* White in light mode */
  border: 1px solid var(--border);
  border-radius: var(--border-radius);
  color: var(--text);
  font-family: var(--font-body);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Dark theme overrides for inputs */
:root.dark-theme .form-group input,
:root.dark-theme .form-group select,
:root.dark-theme textarea,
:root.dark-theme .post-form textarea,
:root.dark-theme input[type="text"],
:root.dark-theme input[type="email"],
:root.dark-theme input[type="password"],
:root.dark-theme select {
  background: rgba(0, 0, 0, 0.2);
  color: var(--text);
}

/* Focus states */
.form-group input:focus,
.form-group select:focus,
textarea:focus,
.post-form textarea:focus,
input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
select:focus {
  border-color: var(--secondary);
  outline: none;
  box-shadow: var(--glow);
}

/* Placeholder text */
::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

/* Icons and symbols within inputs */
.form-group svg,
.input-icon,
.form-icon {
  color: var(--text);
}

:root.dark-theme .form-group svg,
:root.dark-theme .input-icon,
:root.dark-theme .form-icon {
  color: var(--text-secondary);
}

.form-note {
  text-align: center;
  margin-top: var(--spacing-md);
  font-size: 0.9rem;
  color: var(--text-secondary);
}

/* Form elements - updated for theme consistency */
select,
.form-group select,
.filter-group select,
.projects-filters select,
select[name],
.select-wrapper select {
  width: 100%;
  padding: 0.8rem;
  background-color: var(--background);
  border: 1px solid var(--border);
  border-radius: var(--border-radius);
  color: var(--text);
  font-family: var(--font-body);
  appearance: none;
  transition: border-color 0.3s ease, box-shadow 0.3s ease,
    background-color 0.3s ease;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='currentColor' class='chevron-down' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14L2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 16px 12px;
}

/* Dark theme overrides for select elements */
:root.dark-theme select,
:root.dark-theme .form-group select,
:root.dark-theme .filter-group select,
:root.dark-theme .projects-filters select,
:root.dark-theme select[name],
:root.dark-theme .select-wrapper select {
  background-color: var(--background);
  color: var(--text);
  border-color: var(--border);
}

/* Focus states for select elements */
select:focus,
.form-group select:focus,
.filter-group select:focus,
.projects-filters select:focus,
select[name]:focus,
.select-wrapper select:focus {
  border-color: var(--secondary);
  outline: none;
  box-shadow: var(--glow);
}

/* Select dropdown (options) styling */
select option {
  background-color: var(--background);
  color: var(--text);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .project-grid,
  .event-carousel {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .navbar-links,
  .navbar-auth {
    display: none;
  }

  .navbar-toggle {
    display: flex;
  }

  .navbar.active .navbar-links {
    display: flex;
    flex-direction: column;
    position: absolute;
    top: var(--navbar-height);
    left: 0;
    width: 100%;
    background: var(--primary);
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--border);
  }

  .navbar.active .navbar-auth {
    display: flex;
    position: absolute;
    top: calc(var(--navbar-height) + 200px);
    left: 0;
    width: 100%;
    justify-content: center;
    background: var(--primary);
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--border);
  }

  .hero-buttons {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .footer-content {
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  .footer-links {
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  .event-card {
    flex-direction: column;
  }

  .event-date {
    flex-direction: row;
    justify-content: flex-start;
    border-right: none;
    border-bottom: 1px solid var(--border);
    margin-right: 0;
    margin-bottom: var(--spacing-sm);
    padding-bottom: var(--spacing-sm);
  }

  .event-date .day {
    margin-right: var(--spacing-xs);
  }

  .event-cta {
    margin-left: 0;
    margin-top: var(--spacing-sm);
  }

  .glitch {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .project-grid,
  .event-carousel {
    grid-template-columns: 1fr;
  }

  .section-header h2 {
    font-size: 1.5rem;
  }

  .hero h1 {
    font-size: 2rem;
  }

  .hero p {
    font-size: 1.15rem;
  }
}

/* Form styling enhancements for modals */
.modal .form-group input {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border);
  transition: all 0.3s ease;
}

.modal .form-group input:focus {
  border-color: var(--secondary);
  box-shadow: var(--glow);
}

:root.dark-theme .modal .form-group input {
  background: rgba(0, 0, 0, 0.2);
}

.modal h2 {
  margin-bottom: var(--spacing-lg);
  text-align: center;
  font-size: 1.8rem;
}

.modal .btn-block {
  margin-top: var(--spacing-lg);
}

.form-note {
  text-align: center;
  margin-top: var(--spacing-md);
  font-size: 0.9rem;
  color: var(--text-secondary);
}

/* Add a subtle animation for the modal content */
@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal.active .modal-content {
  animation: modalFadeIn 0.3s ease forwards;
}

.welcome-section {
  padding: var(--spacing-xl) 0;
  /* background: linear-gradient(
    90deg,
    var(--primary) 60%,
    var(--background) 100%
  ); */
}

.welcome-flex {
  display: flex;
  gap: var(--spacing-lg);
  justify-content: center;
  align-items: stretch;
  flex-wrap: wrap;
}

.welcome-box {
  flex: 1 1 250px;
  background: var(--card-bg, #fff);
  border-radius: var(--card-radius, 16px);
  box-shadow: var(--shadow, 0 2px 16px rgba(0, 0, 0, 0.08));
  padding: var(--spacing-lg, 2rem);
  text-align: center;
  border: 1px solid var(--border, #e0e0e0);
  transition: transform 0.3s, box-shadow 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.welcome-box:hover {
  /* transform: translateY(-8px) scale(1.03); */
  box-shadow: var(--glow, 0 0 16px #00f5ff55);
  border-color: var(--secondary, #0077b6);
}

.welcome-img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 50%;
  margin-bottom: var(--spacing-md, 1rem);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  background: #fff;
}

.welcome-box h3 {
  color: var(--secondary, #0077b6);
  margin-bottom: var(--spacing-md, 1rem);
  font-size: 1.3rem;
}

.welcome-box p {
  color: var(--text-secondary, #555);
  font-size: 1.05rem;
}

@media (max-width: 900px) {
  .welcome-flex {
    flex-direction: column;
    gap: var(--spacing-md, 1.5rem);
  }
}
