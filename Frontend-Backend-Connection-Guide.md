# Frontend-Backend Connection Issues & Solutions

## Overview
This document outlines all the issues preventing proper connection between the React frontend (`Frontend/aloo`) and FastAPI backend (`mesl_backend`) and provides step-by-step solutions.

## 🚨 Critical Issues Found

### 1. **Missing Users API Router** ⚠️
**Problem**: The frontend is trying to access `/users/` endpoint, but there's no users router in the backend.

**Frontend Code Expecting**:
```typescript
// In src/lib/api.ts
export const usersApi = {
  async list(): Promise<UserResponse[]> {
    const url = new URL("/users/", API_BASE_URL);
    return handle(fetch(url.toString()));
  },
};
```

**Backend Missing**: No `users.py` router file exists in `mesl_backend/api/routers/`

**Solution**: Create a users router in the backend.

### 2. **Backend Environment Configuration** ⚠️
**Problem**: Backend `.env` file is configured but may not be properly loaded.

**Current Backend .env**:
```
SUPABASE_URL = 'https://pwwchsihifevqowsnboz.supabase.co'
SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
POSTGRES_SERVER = 'aws-1-ap-south-1.pooler.supabase.com'
POSTGRES_PORT = 5432
POSTGRES_USER = 'postgres.pwwchsihifevqowsnboz'
POSTGRES_PASSWORD = 'SalmanSajib'
POSTGRES_DB = 'postgres'
```

**Issues**:
- Environment variables have spaces around `=` which may cause parsing issues
- Need to verify database connection

### 3. **CORS Configuration** ✅ (Properly Configured)
**Status**: CORS is properly configured in `main.py`:
```python
origins = [
    "http://localhost:8080",  # React dev server
    "http://127.0.0.1:3000",
    "https://your-frontend-domain.com"
]
```

### 4. **API Base URL Configuration** ✅ (Properly Configured)
**Status**: Frontend is correctly configured to use `http://localhost:8000`:
```typescript
// Frontend .env
VITE_API_BASE_URL=http://localhost:8000

// Frontend env.ts
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || "http://localhost:8000";
```

### 5. **Authentication Flow Issues** ⚠️
**Problem**: Mixed authentication approaches causing confusion.

**Issues**:
- Frontend uses both Supabase auth AND backend auth tokens
- Token storage and management is inconsistent
- Backend expects Bearer tokens but frontend may not always send them

## 📋 Step-by-Step Solutions

### Step 1: Fix Backend Environment Variables
1. **Edit `mesl_backend/.env`** - Remove spaces around `=`:
```bash
SUPABASE_URL=https://pwwchsihifevqowsnboz.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3d2Noc2loaWZldnFvd3NuYm96Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NzgxOTc4MSwiZXhwIjoyMDczMzk1NzgxfQ.kJMEzjFvZ4-l8ToTo25JMLKpMrdK4fTxZarw40Ynv80
SUPABASE_JWT_DISCOVERY_URL=CVSIkqI2cYsevdhHZn6k8yTWtyaIwVGCNgMiyZOWvxvX5XfpvyO6yEhYjRPD3NyBegwjZRjSLyLsfqcKs884AA==
POSTGRES_SERVER=aws-1-ap-south-1.pooler.supabase.com
POSTGRES_PORT=5432
POSTGRES_USER=postgres.pwwchsihifevqowsnboz
POSTGRES_PASSWORD=SalmanSajib
POSTGRES_DB=postgres
FIRST_ADMIN_EMAIL=<EMAIL>
FIRST_ADMIN_PASSWORD=securepassword
```

### Step 2: Create Missing Users API Router
1. **Create `mesl_backend/api/routers/users.py`**:
```python
from fastapi import APIRouter
from sqlmodel import select
from typing import List
from api.deps import SessionDep
from models import User
import uuid

router = APIRouter(prefix="/users", tags=["users"])

@router.get("/", response_model=List[dict])
def list_users(session: SessionDep):
    users = session.exec(select(User)).all()
    return [
        {
            "id": str(user.id),
            "username": user.username,
            "full_name": user.full_name,
            "status": user.status.value if hasattr(user.status, 'value') else str(user.status),
            "department": user.department.value if hasattr(user.department, 'value') else str(user.department)
        }
        for user in users
    ]
```

2. **Add users router to `main.py`**:
```python
# Add this import
from api.routers import events, login, project, topics, feed, users

# Add this line after other router includes
app.include_router(users.router)
```

### Step 3: Initialize Backend Database
1. **Run the prestart script**:
```bash
cd mesl_backend
python prestart.py
```

### Step 4: Start Both Services
1. **Start Backend** (in `mesl_backend` directory):
```bash
# Using FastAPI CLI
fastapi dev main.py

# Or using uvicorn directly
uvicorn main:app --reload --host 127.0.0.1 --port 8000
```

2. **Start Frontend** (in `Frontend/aloo` directory):
```bash
npm run dev
```

### Step 5: Test Connection
1. **Test Backend API directly**:
```bash
# Test if backend is running
curl http://localhost:8000/docs

# Test users endpoint (after creating the router)
curl http://localhost:8000/users/

# Test projects endpoint
curl http://localhost:8000/project/
```

2. **Check Frontend Console** for any remaining API errors

## 🔧 Additional Fixes Needed

### Fix Authentication Token Handling
**Issue**: Inconsistent token management between Supabase and backend auth.

**Recommendation**: Choose one authentication approach:
- **Option A**: Use only Supabase auth (remove backend auth)
- **Option B**: Use only backend auth (remove Supabase auth from frontend)

### Fix API Response Type Mismatches
**Issue**: Some API responses don't match frontend TypeScript interfaces.

**Solution**: Ensure backend responses match frontend type definitions in `src/lib/api.ts`.

## 🚀 Quick Start Commands

```bash
# Backend setup
cd mesl_backend
python prestart.py
fastapi dev main.py

# Frontend setup (in new terminal)
cd Frontend/aloo
npm install
npm run dev
```

## 📝 Verification Checklist

- [ ] Backend starts without errors on port 8000
- [ ] Frontend starts without errors on port 8080
- [ ] `/users/` endpoint returns data
- [ ] `/project/` endpoint returns data
- [ ] `/posts/` endpoint returns data
- [ ] `/events/` endpoint returns data
- [ ] `/topics/` endpoint returns data
- [ ] Authentication works properly
- [ ] No CORS errors in browser console
- [ ] Database connection is successful

## 🐛 Common Issues & Troubleshooting

1. **Port conflicts**: Ensure ports 8000 and 8080 are available
2. **Database connection**: Verify Supabase credentials are correct
3. **Environment variables**: Ensure no spaces around `=` in `.env` files
4. **Dependencies**: Run `npm install` in frontend and ensure Python dependencies are installed in backend
5. **CORS errors**: Check that frontend URL is in backend CORS origins list

---

**Next Steps**: After implementing these fixes, both frontend and backend should connect properly. Test each API endpoint individually before testing the full application flow.
