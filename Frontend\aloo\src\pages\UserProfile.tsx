import { useState, useEffect } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { useAuth } from "@/hooks/useAuth";
import Layout from "@/components/Layout";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Loader2 } from "lucide-react";
import {
  Mail,
  Phone,
  MapPin,
  Calendar,
  Github,
  Linkedin,
  Twitter,
  Globe,
  Award,
  ExternalLink,
  Edit,
  Save,
  X,
  Plus,
  Trash2,
} from "lucide-react";

interface Project {
  id: number;
  title: string;
  role: string;
  status: string;
  description: string;
}

interface SocialLinks {
  github: string;
  linkedin: string;
  twitter: string;
  website: string;
}

interface Profile {
  name: string;
  position: string;
  bio: string;
  email: string;
  phone: string;
  location: string;
  joinDate: string;
  avatar: string;
  socialLinks: SocialLinks;
  skills: string[];
  projects: Project[];
  achievements: string[];
}

const initialProfile: Profile = {
  name: "Dr. <PERSON>",
  position: "AI Research Director",
  bio: "Passionate AI researcher with over 10 years of experience in machine learning, neural networks, and robotics. Leading research in brain-computer interfaces and autonomous systems.",
  email: "<EMAIL>",
  phone: "+****************",
  location: "San Francisco, CA",
  joinDate: "January 2020",
  avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80",
  socialLinks: {
    github: "https://github.com/sarahchen",
    linkedin: "https://linkedin.com/in/sarahchen",
    twitter: "https://twitter.com/sarahchen_ai",
    website: "https://sarahchen.ai",
  },
  skills: [
    "Machine Learning",
    "Neural Networks",
    "Python",
    "TensorFlow",
    "Computer Vision",
    "Robotics",
    "PyTorch",
    "Data Science",
  ],
  projects: [
    {
      id: 1,
      title: "Neural Interface",
      role: "Principal Investigator",
      status: "Active",
      description: "Brain-computer interface technology for assistive devices",
    },
    {
      id: 2,
      title: "Autonomous Drone Swarm",
      role: "Co-Investigator",
      status: "Active",
      description: "Multi-drone coordination for search and rescue operations",
    },
  ],
  achievements: [
    "Best Paper Award - ICML 2023",
    "NSF Career Award 2022",
    "IEEE Fellow 2021",
  ],
};

const UserProfile = () => {
  const { isAuthenticated, isLoading, user } = useAuth();
  const navigate = useNavigate();
  const [isEditing, setIsEditing] = useState(false);
  const [profile, setProfile] = useState<Profile>({ ...initialProfile });
  const [draft, setDraft] = useState({ ...initialProfile });
  const [newSkill, setNewSkill] = useState("");
  const [newAchievement, setNewAchievement] = useState("");
  const [newProject, setNewProject] = useState<Omit<Project, 'id'>>({ 
    title: "", 
    role: "", 
    status: "Active", 
    description: "" 
  });
  const [isAddingProject, setIsAddingProject] = useState(false);

  // Redirect to home if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate('/');
    }
  }, [isAuthenticated, isLoading, navigate]);

  // Show loading state while checking auth
  if (isLoading || !isAuthenticated) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
        </div>
      </Layout>
    );
  }

  // Projects state for adding
  const emptyProject = {
    id: Date.now(),
    title: "",
    role: "",
    status: "Planning",
    description: "",
  };

  const startEdit = () => {
    setDraft({
      ...profile,
      skills: [...profile.skills],
      projects: profile.projects.map((p) => ({ ...p })),
      socialLinks: { ...profile.socialLinks },
      achievements: [...profile.achievements],
    });
    setIsEditing(true);
  };

  const cancelEdit = () => {
    setIsEditing(false);
    setNewSkill("");
    setNewAchievement("");
  };

  const saveEdit = () => {
    setProfile({
      ...draft,
      skills: [...draft.skills],
      projects: draft.projects.map((p: any) => ({ ...p })),
      socialLinks: { ...draft.socialLinks },
      achievements: [...draft.achievements],
    } as any);
    setIsEditing(false);
  };

  return (
    <Layout>
      <div className="page-content container mx-auto px-4 py-8">
        {/* Header */}
        <Card className="project-detail-card mb-8 overflow-hidden">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row items-start gap-6">
              {/* Avatar */}
              <div className="flex-shrink-0">
                <div className="w-32 h-32 md:w-40 md:h-40 rounded-full bg-gradient-to-br from-[#00F5FF]/20 to-[#0466C8]/20 p-1">
                  <img
                    src={isEditing ? draft.avatar : profile.avatar}
                    alt={profile.name}
                    className="w-full h-full rounded-full border-2 border-[#00F5FF]/40 object-cover"
                  />
                </div>
                {isEditing && (
                  <div className="mt-3">
                    <Input
                      value={draft.avatar}
                      onChange={(e) => setDraft({ ...draft, avatar: e.target.value })}
                      placeholder="Avatar image URL"
                      className="bg-[var(--background)] border-[var(--secondary)]/50 text-sm"
                    />
                  </div>
                )}
              </div>

              {/* Name + Position */}
              <div className="flex-1 min-w-0 w-full">
                <div className="flex items-start justify-between gap-3">
                  <div className="w-full">
                    {isEditing ? (
                      <div className="space-y-2">
                        <Input
                          value={draft.name}
                          onChange={(e) => setDraft({ ...draft, name: e.target.value })}
                          className="text-2xl md:text-3xl font-['Orbitron'] font-bold text-[#00F5FF] bg-transparent border-[#00F5FF]/30"
                        />
                        <Input
                          value={draft.position}
                          onChange={(e) => setDraft({ ...draft, position: e.target.value })}
                          className="text-lg text-[var(--text-secondary)] bg-transparent border-[#00F5FF]/30"
                        />
                      </div>
                    ) : (
                      <>
                        <h1 className="text-2xl md:text-3xl font-['Orbitron'] font-bold text-[#00F5FF]">
                          {profile.name}
                        </h1>
                        <p className="text-[var(--text-secondary)] font-['Rajdhani'] text-lg">
                          {profile.position}
                        </p>
                      </>
                    )}
                  </div>

                  {/* Edit Controls */}
                  <div className="flex gap-2">
                    {isEditing ? (
                      <>
                        <Button onClick={saveEdit} size="sm" className="bg-green-500 hover:bg-green-600 text-white">
                          <Save className="mr-2 h-4 w-4" /> Save
                        </Button>
                        <Button onClick={cancelEdit} size="sm" variant="outline" className="border-red-500 text-red-500 hover:bg-red-500 hover:text-white">
                          <X className="mr-2 h-4 w-4" /> Cancel
                        </Button>
                      </>
                    ) : (
                      <Button onClick={startEdit} size="sm" className="bg-[var(--accent)] text-[var(--text)] hover:bg-[var(--secondary)] hover:text-[var(--text)]">
                        <Edit className="mr-2 h-4 w-4" /> Edit Profile
                      </Button>
                    )}
                  </div>
                </div>

                {/* Socials */}
                <div className="flex gap-3 mt-4 flex-wrap">
                  {profile.socialLinks.github && (
                    <a
                      href={profile.socialLinks.github}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-10 h-10 rounded-full bg-[var(--card-bg)] border border-[var(--border)] flex items-center justify-center hover:border-[#00F5FF] hover:bg-[#00F5FF]/10 transition-all"
                    >
                      <Github className="h-5 w-5 text-[var(--text-secondary)]" />
                    </a>
                  )}
                  {profile.socialLinks.linkedin && (
                    <a
                      href={profile.socialLinks.linkedin}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-10 h-10 rounded-full bg-[var(--card-bg)] border border-[var(--border)] flex items-center justify-center hover:border-[#0077B5] hover:bg-[#0077B5]/10 transition-all"
                    >
                      <Linkedin className="h-5 w-5 text-[var(--text-secondary)]" />
                    </a>
                  )}
                  {profile.socialLinks.twitter && (
                    <a
                      href={profile.socialLinks.twitter}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-10 h-10 rounded-full bg-[var(--card-bg)] border border-[var(--border)] flex items-center justify-center hover:border-[#1DA1F2] hover:bg-[#1DA1F2]/10 transition-all"
                    >
                      <Twitter className="h-5 w-5 text-[var(--text-secondary)]" />
                    </a>
                  )}
                  {profile.socialLinks.website && (
                    <a
                      href={profile.socialLinks.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-10 h-10 rounded-full bg-[var(--card-bg)] border border-[var(--border)] flex items-center justify-center hover:border-[#00F5FF] hover:bg-[#00F5FF]/10 transition-all"
                    >
                      <Globe className="h-5 w-5 text-[var(--text-secondary)]" />
                    </a>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Column */}
          <div className="lg:col-span-2 space-y-8">
            {/* About */}
            <Card className="project-detail-card">
              <CardHeader>
                <h2 className="font-['Orbitron'] text-2xl font-bold text-[#00F5FF]">About</h2>
              </CardHeader>
              <CardContent>
                {isEditing ? (
                  <Textarea
                    value={(draft as any).bio}
                    onChange={(e) => setDraft({ ...draft, bio: e.target.value })}
                    rows={4}
                    className="bg-[var(--background)] border-[var(--secondary)]/50 text-[var(--text)]"
                  />
                ) : (
                  <p className="text-[var(--text-secondary)] font-['Rajdhani'] text-lg leading-relaxed">
                    {profile.bio}
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Skills */}
            <Card className="project-detail-card">
              <CardHeader>
                <h2 className="font-['Orbitron'] text-2xl font-bold text-[#00F5FF]">Skills</h2>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2 mb-4">
                  {(isEditing ? draft.skills : profile.skills).map((s, i) => (
                    <div key={i} className="relative">
                      <Badge className="bg-[#0466C8]/20 text-[#00F5FF] border border-[#0466C8]/30 pr-6">
                        {s}
                      </Badge>
                      {isEditing && (
                        <button
                          onClick={() =>
                            setDraft({
                              ...draft,
                              skills: draft.skills.filter((_, idx) => idx !== i),
                            })
                          }
                          className="absolute -right-2 -top-2 w-6 h-6 rounded-full bg-red-500 text-white flex items-center justify-center"
                        >
                          <Trash2 className="w-3 h-3" />
                        </button>
                      )}
                    </div>
                  ))}
                </div>
                {isEditing && (
                  <div className="flex gap-2">
                    <Input
                      value={newSkill}
                      onChange={(e) => setNewSkill(e.target.value)}
                      placeholder="Add a skill"
                      className="bg-[var(--background)] border-[var(--secondary)]/50"
                      onKeyDown={(e) => {
                        if (e.key === "Enter" && newSkill.trim()) {
                          setDraft({ ...draft, skills: [...draft.skills, newSkill.trim()] });
                          setNewSkill("");
                        }
                      }}
                    />
                    <Button
                      size="sm"
                      className="bg-[var(--accent)] text-[var(--text)] hover:bg-[var(--secondary)]"
                      onClick={() => {
                        if (!newSkill.trim()) return;
                        setDraft({ ...draft, skills: [...draft.skills, newSkill.trim()] });
                        setNewSkill("");
                      }}
                    >
                      <Plus className="w-4 h-4" />
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Projects */}
            <Card className="project-detail-card">
              <CardHeader>
                <h2 className="font-['Orbitron'] text-2xl font-bold text-[#00F5FF]">Projects</h2>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {(isEditing ? draft.projects : profile.projects).map((project, idx) => (
                    <div
                      key={project.id}
                      className="border border-[var(--border)] rounded-lg p-4 hover:border-[#00F5FF]/50 transition-colors"
                    >
                      {isEditing ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          <Input
                            value={project.title}
                            onChange={(e) => {
                              const p = [...draft.projects];
                              p[idx] = { ...p[idx], title: e.target.value };
                              setDraft({ ...draft, projects: p });
                            }}
                            placeholder="Title"
                            className="bg-[var(--background)] border-[var(--secondary)]/40"
                          />
                          <Input
                            value={project.role}
                            onChange={(e) => {
                              const p = [...draft.projects];
                              p[idx] = { ...p[idx], role: e.target.value };
                              setDraft({ ...draft, projects: p });
                            }}
                            placeholder="Role"
                            className="bg-[var(--background)] border-[var(--secondary)]/40"
                          />
                          <Input
                            value={project.status}
                            onChange={(e) => {
                              const p = [...draft.projects];
                              p[idx] = { ...p[idx], status: e.target.value };
                              setDraft({ ...draft, projects: p });
                            }}
                            placeholder="Status (Active/Completed/Planning)"
                            className="bg-[var(--background)] border-[var(--secondary)]/40"
                          />
                          <Textarea
                            value={project.description}
                            onChange={(e) => {
                              const p = [...draft.projects];
                              p[idx] = { ...p[idx], description: e.target.value };
                              setDraft({ ...draft, projects: p });
                            }}
                            rows={3}
                            placeholder="Description"
                            className="bg-[var(--background)] border-[var(--secondary)]/40 md:col-span-2"
                          />
                          <div className="flex justify-end md:col-span-2">
                            <Button
                              variant="outline"
                              className="border-red-500 text-red-500 hover:bg-red-500 hover:text-white"
                              onClick={() =>
                                setDraft({
                                  ...draft,
                                  projects: draft.projects.filter((_, i) => i !== idx),
                                })
                              }
                            >
                              <Trash2 className="w-4 h-4 mr-2" /> Remove
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <div>
                          <div className="flex items-start justify-between">
                            <div>
                              <h3 className="font-['Orbitron'] text-lg font-semibold text-[#00F5FF]">
                                {project.title}
                              </h3>
                              <p className="text-[var(--text-secondary)] font-['Rajdhani']">
                                {project.role}
                              </p>
                            </div>
                            <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                              {project.status}
                            </Badge>
                          </div>
                          <p className="text-[var(--text-secondary)] text-sm mt-2">
                            {project.description}
                          </p>
                          <Link
                            to={`/projects/${project.id}`}
                            className="inline-flex items-center mt-2 text-[#00F5FF] hover:text-[#00F5FF]/80 text-sm"
                          >
                            View Project <ExternalLink className="ml-1 h-3 w-3" />
                          </Link>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
                {isEditing && (
                  <div className="flex justify-end mt-4">
                    <Button
                      onClick={() =>
                        setDraft({ ...draft, projects: [{ ...emptyProject, id: Date.now() }, ...draft.projects] })
                      }
                      className="bg-[var(--accent)] text-[var(--text)] hover:bg-[var(--secondary)]"
                    >
                      <Plus className="w-4 h-4 mr-2" /> Add Project
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Side Column */}
          <div className="space-y-8">
            {/* Contact + Social Links editing */}
            <Card className="project-detail-card">
              <CardHeader>
                <h2 className="font-['Orbitron'] text-xl font-bold text-[#00F5FF]">Contact</h2>
              </CardHeader>
              <CardContent className="space-y-3 text-[var(--text-secondary)]">
                {isEditing ? (
                  <div className="space-y-3">
                    <div className="flex items-center gap-2"><Mail className="h-4 w-4 text-[#00F5FF]" />
                      <Input value={draft.email} onChange={(e) => setDraft({ ...draft, email: e.target.value })} />
                    </div>
                    <div className="flex items-center gap-2"><Phone className="h-4 w-4 text-[#00F5FF]" />
                      <Input value={draft.phone} onChange={(e) => setDraft({ ...draft, phone: e.target.value })} />
                    </div>
                    <div className="flex items-center gap-2"><MapPin className="h-4 w-4 text-[#00F5FF]" />
                      <Input value={draft.location} onChange={(e) => setDraft({ ...draft, location: e.target.value })} />
                    </div>
                    <div className="flex items-center gap-2"><Calendar className="h-4 w-4 text-[#00F5FF]" />
                      <Input value={draft.joinDate} onChange={(e) => setDraft({ ...draft, joinDate: e.target.value })} />
                    </div>
                    <div className="pt-2 border-t border-[var(--border)] mt-4" />
                    <div className="space-y-2">
                      <div className="flex items-center gap-2"><Github className="h-4 w-4 text-[#00F5FF]" />
                        <Input value={draft.socialLinks.github} onChange={(e) => setDraft({ ...draft, socialLinks: { ...draft.socialLinks, github: e.target.value } })} placeholder="GitHub URL" />
                      </div>
                      <div className="flex items-center gap-2"><Linkedin className="h-4 w-4 text-[#00F5FF]" />
                        <Input value={draft.socialLinks.linkedin} onChange={(e) => setDraft({ ...draft, socialLinks: { ...draft.socialLinks, linkedin: e.target.value } })} placeholder="LinkedIn URL" />
                      </div>
                      <div className="flex items-center gap-2"><Twitter className="h-4 w-4 text-[#00F5FF]" />
                        <Input value={draft.socialLinks.twitter} onChange={(e) => setDraft({ ...draft, socialLinks: { ...draft.socialLinks, twitter: e.target.value } })} placeholder="Twitter URL" />
                      </div>
                      <div className="flex items-center gap-2"><Globe className="h-4 w-4 text-[#00F5FF]" />
                        <Input value={draft.socialLinks.website} onChange={(e) => setDraft({ ...draft, socialLinks: { ...draft.socialLinks, website: e.target.value } })} placeholder="Website URL" />
                      </div>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="flex items-center"><Mail className="mr-2 h-4 w-4 text-[#00F5FF]" /> {profile.email}</div>
                    <div className="flex items-center"><Phone className="mr-2 h-4 w-4 text-[#00F5FF]" /> {profile.phone}</div>
                    <div className="flex items-center"><MapPin className="mr-2 h-4 w-4 text-[#00F5FF]" /> {profile.location}</div>
                    <div className="flex items-center"><Calendar className="mr-2 h-4 w-4 text-[#00F5FF]" /> Joined {profile.joinDate}</div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Achievements */}
            <Card className="project-detail-card">
              <CardHeader>
                <h2 className="font-['Orbitron'] text-xl font-bold text-[#00F5FF]">Achievements</h2>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {(isEditing ? draft.achievements : profile.achievements).map((ach, i) => (
                    <li key={i} className="flex items-start justify-between group">
                      <div className="flex items-start">
                        <Award className="mr-2 h-4 w-4 text-[#00F5FF] mt-0.5" />
                        {isEditing ? (
                          <Input
                            value={ach}
                            onChange={(e) => {
                              const a = [...draft.achievements];
                              a[i] = e.target.value;
                              setDraft({ ...draft, achievements: a });
                            }}
                            className="bg-[var(--background)] border-[var(--secondary)]/40"
                          />
                        ) : (
                          <span className="text-[var(--text-secondary)] text-sm">{ach}</span>
                        )}
                      </div>
                      {isEditing && (
                        <button
                          onClick={() =>
                            setDraft({ ...draft, achievements: draft.achievements.filter((_, idx) => idx !== i) })
                          }
                          className="text-red-400 hover:text-red-300 opacity-100 transition-opacity"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      )}
                    </li>
                  ))}
                </ul>
                {isEditing && (
                  <div className="mt-4 flex gap-2">
                    <Input
                      value={newAchievement}
                      onChange={(e) => setNewAchievement(e.target.value)}
                      placeholder="Add new achievement"
                      className="bg-[var(--background)] border-[var(--secondary)]/50"
                      onKeyDown={(e) => {
                        if (e.key === "Enter" && newAchievement.trim()) {
                          setDraft({ ...draft, achievements: [...draft.achievements, newAchievement.trim()] });
                          setNewAchievement("");
                        }
                      }}
                    />
                    <Button
                      size="sm"
                      className="bg-[var(--accent)] text-[var(--text)] hover:bg-[var(--secondary)]"
                      onClick={() => {
                        if (!newAchievement.trim()) return;
                        setDraft({ ...draft, achievements: [...draft.achievements, newAchievement.trim()] });
                        setNewAchievement("");
                      }}
                    >
                      <Plus className="w-4 h-4" />
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default UserProfile;
