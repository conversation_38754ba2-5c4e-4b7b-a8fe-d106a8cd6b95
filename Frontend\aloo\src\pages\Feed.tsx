import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import Navbar from "@/components/Navbar";

import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import LoginButton from "@/components/LoginButton";
import { useAuth } from "@/hooks/useAuth";
import {
  IoHeart,
  IoHeartOutline,
  IoChatbubbleOutline,
  IoLogIn,
  IoSend,
  IoTrash,
  IoCreate,
  IoImages,
  IoClose,
} from "react-icons/io5";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

import {
  postsApi,
  usersApi,
  type PostRead,
  type CommentRead,
  type UserResponse,
} from "@/lib/api";

// Format: "date MonthName, year" (e.g., "2 October, 2025")
const formatPostDate = (iso: string) => {
  const d = new Date(iso);
  const day = d.getDate();
  const month = d.toLocaleString("en-US", { month: "long" });
  const year = d.getFullYear();
  return `${day} ${month}, ${year}`;
};

interface CommentWithUI extends CommentRead {
  isReplying?: boolean;
  newReply?: string;
}

interface PostWithUI extends PostRead {
  isLiked?: boolean;
  showComments?: boolean;
  isEditing?: boolean;
  newComment?: string;
  comments?: CommentWithUI[];
}

const Feed = () => {
  const navigate = useNavigate();
  const [newPost, setNewPost] = useState("");
  const [posts, setPosts] = useState<PostWithUI[]>([]);
  const [users, setUsers] = useState<UserResponse[]>([]);
  const { user, isAuthenticated } = useAuth();
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [editingPost, setEditingPost] = useState<string | null>(null);
  const [editContent, setEditContent] = useState("");
  const [userLikes, setUserLikes] = useState<Set<string>>(new Set()); // Track which posts user has liked
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [totalUsers, setTotalUsers] = useState<number>(0);
  const [editNewImages, setEditNewImages] = useState<File[]>([]);
  const [editRemoveImageIds, setEditRemoveImageIds] = useState<Set<string>>(
    new Set()
  );
  const [confirmDeleteId, setConfirmDeleteId] = useState<string | null>(null);

  // Load users from backend
  const loadUsers = async () => {
    try {
      const usersList = await usersApi.list();
      setUsers(usersList);
    } catch (err: any) {
      console.error("Failed to load users:", err);
    }
  };

  // Load total users count
  const loadTotalUsers = async () => {
    try {
      const result = await usersApi.getTotalCount();
      setTotalUsers(result.total);
    } catch (err: any) {
      console.error("Failed to load total users count:", err);
    }
  };

  // Helper function to get user display name from author object or user lookup
  const getUserDisplayName = (
    author?: { id: string; username: string; full_name?: string } | null,
    userId?: string
  ) => {
    if (author) {
      return author.full_name || author.username || "Unknown User";
    }
    if (userId) {
      const user = users.find((u) => u.id === userId);
      if (user) {
        return user.full_name || user.username || "Unknown User";
      }
    }
    return "Unknown User";
  };

  // Helper function to get user initials for avatar
  const getUserInitials = (
    author?: { id: string; username: string; full_name?: string } | null,
    userId?: string
  ) => {
    if (author) {
      const name = author.full_name || author.username || "??";
      return name.slice(0, 2).toUpperCase();
    }
    if (userId) {
      const user = users.find((u) => u.id === userId);
      if (user) {
        const name = user.full_name || user.username || "??";
        return name.slice(0, 2).toUpperCase();
      }
    }
    return "??";
  };

  // Load posts from backend
  const loadPosts = async () => {
    try {
      setLoading(true);
      const list = await postsApi.list({ limit: 50, offset: 0 });
      const mapped: PostWithUI[] = list.map((p) => ({
        ...p,
        isLiked: userLikes.has(p.id), // Check if current user has liked this post
        showComments: false,
        newComment: "",
        comments: p.comments?.map((comment) => ({
          ...comment,
          isReplying: false,
          newReply: "",
        })),
      }));
      setPosts(mapped);
      setError(null);
    } catch (err) {
      console.error("Failed to load posts:", err);
      setError("Unable to load feed right now. Please try again later.");
      setPosts([]);
    } finally {
      setLoading(false);
    }
  };

  // Load user's likes from localStorage
  useEffect(() => {
    if (user?.id) {
      const storedLikes = localStorage.getItem(`userLikes_${user.id}`);
      if (storedLikes) {
        try {
          const likedPostIds = JSON.parse(storedLikes);
          setUserLikes(new Set(likedPostIds));
        } catch (err) {
          console.error("Failed to parse stored likes:", err);
        }
      }
    }
  }, [user?.id]);

  useEffect(() => {
    loadUsers();
    loadPosts();
    loadTotalUsers();
  }, [user?.id]); // Reload when user changes

  const handleLike = async (postId: string) => {
    if (!isAuthenticated) {
      alert("Please log in to like posts.");
      return;
    }

    const wasLiked = userLikes.has(postId);
    const newIsLiked = !wasLiked;

    // Optimistic update for instant feedback
    setUserLikes((prev) => {
      const newSet = new Set(prev);
      if (newIsLiked) {
        newSet.add(postId);
      } else {
        newSet.delete(postId);
      }
      return newSet;
    });

    setPosts((currentPosts) =>
      currentPosts.map((post) => {
        if (post.id === postId) {
          return {
            ...post,
            isLiked: newIsLiked,
            likes_count: newIsLiked
              ? (post.likes_count || 0) + 1
              : Math.max((post.likes_count || 0) - 1, 0),
          };
        }
        return post;
      })
    );

    try {
      // Call the backend like endpoint (it toggles the like state)
      await postsApi.like(postId);

      // Store like status in localStorage for persistence (optimized)
      const currentLikes = new Set(userLikes);
      if (newIsLiked) {
        currentLikes.add(postId);
      } else {
        currentLikes.delete(postId);
      }
      localStorage.setItem(
        `userLikes_${user?.id}`,
        JSON.stringify(Array.from(currentLikes))
      );
    } catch (err: any) {
      console.error("Failed to like post:", err);

      // Check if it's an authentication error
      if (
        err.message.includes("401") ||
        err.message.includes("Invalid token")
      ) {
        setError("Your session has expired. Please log in again.");
        // Clear invalid tokens
        sessionStorage.removeItem("access_token");
        sessionStorage.removeItem("refresh_token");
        sessionStorage.removeItem("auth");
      } else {
        setError("Failed to update like status. Please try again.");
      }

      // Revert optimistic update on error
      setUserLikes((prev) => {
        const newSet = new Set(prev);
        if (wasLiked) {
          newSet.add(postId);
        } else {
          newSet.delete(postId);
        }
        return newSet;
      });

      setPosts((currentPosts) =>
        currentPosts.map((post) => {
          if (post.id === postId) {
            return {
              ...post,
              isLiked: wasLiked,
              likes_count: wasLiked
                ? (post.likes_count || 0) + 1
                : Math.max((post.likes_count || 0) - 1, 0),
            };
          }
          return post;
        })
      );
    }
  };

  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const newImages = Array.from(files).filter(
        (file) =>
          file.type.startsWith("image/") && file.size <= 10 * 1024 * 1024 // 10MB limit
      );
      setSelectedImages((prev) => [...prev, ...newImages].slice(0, 5)); // Max 5 images
    }
  };

  const removeImage = (index: number) => {
    setSelectedImages((prev) => prev.filter((_, i) => i !== index));
  };

  // Edit image handlers
  const handleEditImageSelect = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files;
    if (files) {
      const newImages = Array.from(files).filter(
        (file) =>
          file.type.startsWith("image/") && file.size <= 10 * 1024 * 1024
      );
      setEditNewImages((prev) => [...prev, ...newImages].slice(0, 5));
    }
  };

  const removeEditNewImage = (index: number) => {
    setEditNewImages((prev) => prev.filter((_, i) => i !== index));
  };

  const toggleRemoveExistingImage = (imageId: string) => {
    setEditRemoveImageIds((prev) => {
      const next = new Set(prev);
      if (next.has(imageId)) next.delete(imageId);
      else next.add(imageId);
      return next;
    });
  };

  const handleSubmitPost = async () => {
    if (!newPost.trim()) return;
    if (!isAuthenticated) {
      alert("Please log in to create posts.");
      return;
    }

    try {
      setLoading(true);
      const newPostData = await postsApi.create({
        content: newPost,
        images: selectedImages.length > 0 ? selectedImages : undefined,
      });

      // Add the new post to the beginning of the list
      const postWithUI: PostWithUI = {
        ...newPostData,
        isLiked: false,
        showComments: false,
        newComment: "",
      };

      setPosts((prev) => [postWithUI, ...prev]);
      setNewPost("");
      setSelectedImages([]);
      setError(null);
    } catch (err: any) {
      console.error("Failed to create post:", err);
      console.error("Error details:", err.message);

      // Check if it's an authentication error
      if (
        err.message.includes("401") ||
        err.message.includes("Invalid token")
      ) {
        setError("Your session has expired. Please log in again.");
        // Clear invalid tokens
        sessionStorage.removeItem("access_token");
        sessionStorage.removeItem("refresh_token");
        sessionStorage.removeItem("auth");
      } else {
        setError(`Failed to create post: ${err.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleEditPost = (postId: string, content: string) => {
    setEditingPost(postId);
    setEditContent(content);
    setEditNewImages([]);
    setEditRemoveImageIds(new Set());
  };

  const handleUpdatePost = async (postId: string) => {
    if (
      !editContent.trim() &&
      editNewImages.length === 0 &&
      editRemoveImageIds.size === 0
    )
      return;

    try {
      setLoading(true);
      const updatedPost = await postsApi.update(postId, {
        content: editContent,
        images: editNewImages.length ? editNewImages : undefined,
        remove_image_ids: editRemoveImageIds.size
          ? Array.from(editRemoveImageIds)
          : undefined,
      });

      setPosts(
        posts.map((post) =>
          post.id === postId
            ? {
                ...post,
                content: updatedPost.content,
                images: updatedPost.images,
                updated_at: updatedPost.updated_at,
                is_updated: updatedPost.is_updated,
              }
            : post
        )
      );

      setEditingPost(null);
      setEditContent("");
      setEditNewImages([]);
      setEditRemoveImageIds(new Set());
      setError(null);
    } catch (err: any) {
      console.error("Failed to update post:", err);
      setError("Failed to update post. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePost = async (postId: string) => {
    try {
      setLoading(true);
      await postsApi.remove(postId);
      setPosts(posts.filter((post) => post.id !== postId));
      setError(null);
    } catch (err: any) {
      console.error("Failed to delete post:", err);
      setError("Failed to delete post. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const toggleComments = (postId: string) => {
    setPosts(
      posts.map((post) =>
        post.id === postId
          ? { ...post, showComments: !post.showComments }
          : post
      )
    );
  };

  const handleAddComment = async (postId: string, content: string) => {
    if (!content.trim() || !isAuthenticated) return;

    try {
      const newComment = await postsApi.comment(postId, { content });

      // Update the post's comments
      setPosts(
        posts.map((post) =>
          post.id === postId
            ? {
                ...post,
                comments: [...(post.comments || []), newComment],
                newComment: "",
              }
            : post
        )
      );
    } catch (err: any) {
      console.error("Failed to add comment:", err);
      setError("Failed to add comment. Please try again.");
    }
  };

  const updateNewComment = (postId: string, content: string) => {
    setPosts(
      posts.map((post) =>
        post.id === postId ? { ...post, newComment: content } : post
      )
    );
  };

  const toggleReply = (postId: string, commentId: string) => {
    setPosts(
      posts.map((post) =>
        post.id === postId
          ? {
              ...post,
              comments: post.comments?.map((comment) =>
                comment.id === commentId
                  ? { ...comment, isReplying: !comment.isReplying }
                  : comment
              ),
            }
          : post
      )
    );
  };

  const updateReply = (postId: string, commentId: string, content: string) => {
    setPosts(
      posts.map((post) =>
        post.id === postId
          ? {
              ...post,
              comments: post.comments?.map((comment) =>
                comment.id === commentId
                  ? { ...comment, newReply: content }
                  : comment
              ),
            }
          : post
      )
    );
  };

  const handleAddReply = async (postId: string, commentId: string) => {
    const post = posts.find((p) => p.id === postId);
    const comment = post?.comments?.find((c) => c.id === commentId);
    if (!comment?.newReply?.trim() || !isAuthenticated) return;

    try {
      // Create reply with @mention format
      const replyContent = `@${getUserDisplayName(null, comment.author_id)}: ${
        comment.newReply
      }`;
      const reply = await postsApi.comment(postId, { content: replyContent });

      // Add reply as a new comment with UI state
      const replyWithUI: CommentWithUI = {
        ...reply,
        isReplying: false,
        newReply: "",
      };

      setPosts(
        posts.map((p) =>
          p.id === postId
            ? {
                ...p,
                comments: [...(p.comments || []), replyWithUI],
              }
            : p
        )
      );

      // Clear reply state
      setPosts(
        posts.map((post) =>
          post.id === postId
            ? {
                ...post,
                comments: post.comments?.map((c) =>
                  c.id === commentId
                    ? { ...c, isReplying: false, newReply: "" }
                    : c
                ),
              }
            : post
        )
      );
    } catch (err: any) {
      console.error("Failed to add reply:", err);
      setError("Failed to add reply. Please try again.");
    }
  };

  return (
    <div className="page-content container mx-auto px-4 py-8">
      <Navbar />
      {error && (
        <div className="project-detail-card p-4 mb-6 border-red-500/30 bg-red-500/10 text-red-300">
          {error}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <div className="project-detail-card p-6 sticky top-24">
            <div className="text-center mb-6">
              <Avatar className="w-20 h-20 mx-auto mb-4 border-2 border-[#00F5FF]/30">
                <AvatarImage src="" />
                <AvatarFallback className="bg-[#0466C8] text-white text-2xl">
                  GU
                </AvatarFallback>
              </Avatar>
              <h3 className="font-['Orbitron'] text-lg font-semibold text-[#00F5FF]">
                {isAuthenticated ? user?.name || user?.email : "Guest User"}
              </h3>
              <p className="text-white/60 font-['Rajdhani']">
                {isAuthenticated ? "MESL Researcher" : "Welcome to MESL Feed"}
              </p>
            </div>

            <div className="space-y-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-[#00F5FF]">
                  {posts.length}
                </div>
                <div className="text-sm text-white/60">Total Posts</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-[#00F5FF]">
                  {totalUsers}
                </div>
                <div className="text-sm text-white/60">Total Users</div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Feed */}
        <div className="lg:col-span-3 space-y-6">
          {/* Create Post or Login Reminder */}
          {isAuthenticated ? (
            <Card className="project-detail-card">
              <CardHeader>
                <h2 className="font-['Orbitron'] text-xl font-semibold text-[#00F5FF]">
                  Share Your Thoughts
                </h2>
              </CardHeader>
              <CardContent className="space-y-4">
                <Textarea
                  placeholder="What's on your mind? Share your research insights, project updates, or lab experiences..."
                  value={newPost}
                  onChange={(e) => setNewPost(e.target.value)}
                  className="bg-[var(--background)] border-[var(--secondary)]/50 text-[var(--text)] placeholder:text-[var(--text-secondary)] min-h-[100px]"
                />

                {/* Image Preview */}
                {selectedImages.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {selectedImages.map((image, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={URL.createObjectURL(image)}
                          alt={`Preview ${index + 1}`}
                          className="w-full h-24 object-cover rounded-lg border border-[var(--secondary)]/30"
                        />
                        <button
                          onClick={() => removeImage(index)}
                          className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <IoClose className="w-3 h-3" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <input
                      type="file"
                      id="image-upload"
                      multiple
                      accept="image/*"
                      onChange={handleImageSelect}
                      className="hidden"
                    />
                    <label
                      htmlFor="image-upload"
                      className="flex items-center space-x-2 px-3 py-2 bg-[var(--secondary)]/20 hover:bg-[var(--secondary)]/30 rounded-lg cursor-pointer transition-colors"
                    >
                      <IoImages className="w-4 h-4 text-[#00F5FF]" />
                      <span className="text-sm text-[#00F5FF] font-['Rajdhani']">
                        Add Photos ({selectedImages?.length || 0}/5)
                      </span>
                    </label>
                  </div>

                  <Button
                    onClick={handleSubmitPost}
                    disabled={!newPost.trim() || loading}
                    className="bg-[var(--accent)] text-[var(--text)] hover:bg-[var(--secondary)] hover:text-[var(--text)] transition-colors disabled:opacity-50"
                  >
                    {loading ? "Posting..." : "Post Update"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card className="project-detail-card">
              <CardContent className="p-8 text-center">
                <div className="flex flex-col items-center space-y-4">
                  <IoLogIn className="w-12 h-12 text-[#00F5FF]" />
                  <h3 className="font-['Orbitron'] text-xl font-semibold text-[#00F5FF]">
                    Join the Conversation
                  </h3>
                  <p className="text-white/70 font-['Rajdhani'] text-lg max-w-md">
                    Login to share your research insights, project updates, and
                    connect with fellow researchers.
                  </p>
                  <div className="flex gap-4">
                    <LoginButton
                      className="bg-[var(--accent)] text-[var(--text)] hover:bg-[var(--secondary)] hover:text-[var(--text)] transition-colors px-6 py-2 flex items-center gap-2"
                      variant="login"
                    >
                      <IoLogIn className="h-4 w-4" />
                      Login
                    </LoginButton>
                    <LoginButton
                      className="border border-[#00F5FF] text-[#00F5FF] hover:bg-[#00F5FF] hover:text-[#001233] px-6 py-2 bg-transparent rounded-md transition-colors"
                      variant="signup"
                    >
                      Sign Up
                    </LoginButton>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Feed Posts */}
          {loading && posts.length === 0 && (
            <div className="text-center py-8">
              <div className="text-[#00F5FF]">Loading posts...</div>
            </div>
          )}

          {posts && posts.length > 0 ? (
            posts
              .filter((post) => post && post.id)
              .map((post) => (
                <Card key={post.id} className="project-detail-card">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <Avatar className="w-12 h-12 border-2 border-[#00F5FF]/30">
                        <AvatarFallback className="bg-[#0466C8] text-white">
                          {getUserInitials(post.author)}
                        </AvatarFallback>
                      </Avatar>

                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex flex-col leading-tight">
                            <h3 className="font-['Orbitron'] font-semibold text-[#00F5FF] leading-tight">
                              {getUserDisplayName(post.author)}
                            </h3>
                            <div className="text-white/50 text-sm font-['Rajdhani'] mt-0.5">
                              {formatPostDate(post.created_at)}
                              {post.is_updated && (
                                <span className="text-yellow-400 text-xs ml-2">
                                  (edited)
                                </span>
                              )}
                            </div>
                          </div>

                          {/* Post Actions for Author */}
                          {isAuthenticated && user?.id === post.author?.id && (
                            <div className="flex items-center space-x-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() =>
                                  handleEditPost(post.id, post.content)
                                }
                                className="border-[var(--secondary)]/50 text-[var(--text)] hover:bg-[var(--secondary)]/20"
                              >
                                <IoCreate className="w-4 h-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => setConfirmDeleteId(post.id)}
                                className="border-red-500/50 text-red-400 hover:bg-red-500/20"
                              >
                                <IoTrash className="w-4 h-4" />
                              </Button>
                            </div>
                          )}
                        </div>

                        <div className="mb-4">
                          {editingPost === post.id ? (
                            <div className="space-y-3">
                              <Textarea
                                value={editContent}
                                onChange={(e) => setEditContent(e.target.value)}
                                className="bg-[var(--background)] border-[var(--secondary)]/50 text-[var(--text)] min-h-[100px]"
                              />

                              {/* Existing images with remove toggle */}
                              {post.images && post.images.length > 0 && (
                                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                                  {post.images.map((image) => (
                                    <div
                                      key={image.id}
                                      className={`relative rounded-lg overflow-hidden border ${
                                        editRemoveImageIds.has(image.id)
                                          ? "border-red-500"
                                          : "border-[var(--secondary)]/30"
                                      }`}
                                    >
                                      <img
                                        src={image.file_url}
                                        alt="Existing image"
                                        className="w-full h-24 object-cover"
                                      />
                                      <button
                                        type="button"
                                        onClick={() =>
                                          toggleRemoveExistingImage(image.id)
                                        }
                                        className="absolute top-2 right-2 bg-black/60 text-white text-xs px-2 py-1 rounded"
                                      >
                                        {editRemoveImageIds.has(image.id)
                                          ? "Undo"
                                          : "Remove"}
                                      </button>
                                    </div>
                                  ))}
                                </div>
                              )}

                              {/* New images preview */}
                              {editNewImages.length > 0 && (
                                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                                  {editNewImages.map((image, index) => (
                                    <div key={index} className="relative group">
                                      <img
                                        src={URL.createObjectURL(image)}
                                        alt={`Preview ${index + 1}`}
                                        className="w-full h-24 object-cover rounded-lg border border-[var(--secondary)]/30"
                                      />
                                      <button
                                        type="button"
                                        onClick={() =>
                                          removeEditNewImage(index)
                                        }
                                        className="absolute top-2 right-2 bg-black/60 text-white text-xs px-2 py-1 rounded"
                                      >
                                        Remove
                                      </button>
                                    </div>
                                  ))}
                                </div>
                              )}

                              <div className="flex items-center justify-between">
                                <div>
                                  <input
                                    type="file"
                                    id={`edit-image-upload-${post.id}`}
                                    multiple
                                    accept="image/*"
                                    onChange={handleEditImageSelect}
                                    className="hidden"
                                  />
                                  <label
                                    htmlFor={`edit-image-upload-${post.id}`}
                                    className="flex items-center space-x-2 px-3 py-2 bg-[var(--secondary)]/20 hover:bg-[var(--secondary)]/30 rounded-lg cursor-pointer transition-colors"
                                  >
                                    <IoImages className="w-4 h-4 text-[#00F5FF]" />
                                    <span className="text-sm text-[#00F5FF] font-['Rajdhani']">
                                      Add Photos ({editNewImages.length}/5)
                                    </span>
                                  </label>
                                </div>

                                <div className="flex justify-end space-x-2">
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => {
                                      setEditingPost(null);
                                      setEditContent("");
                                      setEditNewImages([]);
                                      setEditRemoveImageIds(new Set());
                                    }}
                                    className="border-[var(--secondary)]/50 text-[var(--text)]"
                                  >
                                    Cancel
                                  </Button>
                                  <Button
                                    size="sm"
                                    onClick={() => handleUpdatePost(post.id)}
                                    disabled={
                                      (!editContent.trim() &&
                                        editNewImages.length === 0 &&
                                        editRemoveImageIds.size === 0) ||
                                      loading
                                    }
                                    className="bg-[var(--accent)] text-[var(--text)]"
                                  >
                                    {loading ? "Saving..." : "Save"}
                                  </Button>
                                </div>
                              </div>
                            </div>
                          ) : (
                            <div
                              className="font-['Rajdhani'] text-white/80 leading-relaxed cursor-pointer hover:text-white/90 transition-colors"
                              onClick={() => navigate(`/post/${post.id}`)}
                            >
                              {post.content}
                            </div>
                          )}

                          {/* Post Images */}
                          {editingPost !== post.id &&
                            post.images &&
                            post.images.length > 0 && (
                              <div
                                className={`mt-4 ${
                                  post.images.length === 1
                                    ? "grid grid-cols-1"
                                    : post.images.length === 2
                                    ? "grid grid-cols-2 gap-2"
                                    : post.images.length === 3
                                    ? "grid grid-cols-2 gap-2"
                                    : "grid grid-cols-2 gap-2"
                                }`}
                              >
                                {post.images.slice(0, 4).map((image, index) => (
                                  <div
                                    key={image.id}
                                    className={`relative ${
                                      post.images!.length === 3 && index === 0
                                        ? "col-span-2"
                                        : ""
                                    }`}
                                  >
                                    <img
                                      src={image.file_url}
                                      alt={`Post image ${index + 1}`}
                                      className={`w-full object-cover rounded-lg cursor-pointer hover:opacity-90 transition-opacity ${
                                        post.images!.length === 1
                                          ? "h-80"
                                          : post.images!.length === 2
                                          ? "h-64"
                                          : "h-48"
                                      }`}
                                      onClick={() =>
                                        navigate(`/post/${post.id}`)
                                      }
                                    />
                                    {post.images!.length > 4 && index === 3 && (
                                      <div className="absolute inset-0 bg-black/60 rounded-lg flex items-center justify-center">
                                        <span className="text-white text-xl font-semibold">
                                          +{post.images!.length - 4}
                                        </span>
                                      </div>
                                    )}
                                  </div>
                                ))}
                              </div>
                            )}
                        </div>

                        <div className="flex items-center space-x-6 text-white/60">
                          <button
                            onClick={() => handleLike(post.id)}
                            disabled={!isAuthenticated}
                            className={`flex items-center space-x-2 transition-all duration-200 disabled:opacity-50 ${
                              post.isLiked
                                ? "text-red-400 drop-shadow-[0_0_8px_rgba(239,68,68,0.6)] hover:drop-shadow-[0_0_12px_rgba(239,68,68,0.8)]"
                                : "text-white/60 hover:text-red-400"
                            }`}
                          >
                            {post.isLiked ? (
                              <IoHeart className="w-5 h-5" />
                            ) : (
                              <IoHeartOutline className="w-5 h-5" />
                            )}
                            <span className="font-['Rajdhani'] font-semibold">
                              {post.likes_count || 0}
                            </span>
                          </button>

                          <button
                            onClick={() => toggleComments(post.id)}
                            className="flex items-center space-x-2 hover:text-[#00F5FF] transition-colors"
                          >
                            <IoChatbubbleOutline className="w-5 h-5" />
                            <span className="font-['Rajdhani']">
                              {post.comments?.length || 0}
                            </span>
                          </button>
                        </div>

                        {/* Comments Section */}
                        {post.showComments && (
                          <div className="mt-4 pt-4 border-t border-[var(--secondary)]/30">
                            {/* Add Comment - Facebook Style */}
                            {isAuthenticated && (
                              <div className="flex space-x-3 mb-4">
                                <Avatar className="w-8 h-8 border border-[#00F5FF]/30 flex-shrink-0">
                                  <AvatarFallback className="bg-[#0466C8] text-white text-sm">
                                    {user?.id
                                      ? getUserInitials(null, user.id)
                                      : "??"}
                                  </AvatarFallback>
                                </Avatar>
                                <div className="flex-1 flex items-end gap-2">
                                  <textarea
                                    placeholder="Write a comment..."
                                    value={post.newComment || ""}
                                    onChange={(e) =>
                                      updateNewComment(post.id, e.target.value)
                                    }
                                    className="flex-1 bg-white text-gray-900 placeholder-gray-500 text-sm px-3 py-2 resize-none overflow-hidden border border-gray-300 rounded-2xl outline-none min-h-[36px]"
                                    rows={1}
                                    style={{
                                      height: "auto",
                                      minHeight: "36px",
                                    }}
                                    onInput={(e) => {
                                      const target =
                                        e.target as HTMLTextAreaElement;
                                      target.style.height = "auto";
                                      target.style.height =
                                        target.scrollHeight + "px";
                                    }}
                                    onKeyPress={(e) => {
                                      if (
                                        e.key === "Enter" &&
                                        !e.shiftKey &&
                                        post.newComment?.trim()
                                      ) {
                                        e.preventDefault();
                                        handleAddComment(
                                          post.id,
                                          post.newComment
                                        );
                                      }
                                    }}
                                  />
                                  {post.newComment?.trim() && (
                                    <Button
                                      size="sm"
                                      onClick={() =>
                                        post.newComment &&
                                        handleAddComment(
                                          post.id,
                                          post.newComment
                                        )
                                      }
                                      className="bg-[#1877f2] hover:bg-[#166fe5] text-white rounded-full px-4 py-1 text-xs font-semibold h-7 mb-0.5"
                                    >
                                      Comment
                                    </Button>
                                  )}
                                </div>
                              </div>
                            )}

                            {/* Comments List - Facebook/Instagram Style */}
                            <div className="space-y-2">
                              {post.comments?.map((comment) => {
                                const isReply = comment.content.startsWith("@");
                                return (
                                  <div
                                    key={comment.id}
                                    className={`${
                                      isReply
                                        ? "ml-8 border-l-2 border-[#00F5FF]/20 pl-4"
                                        : ""
                                    }`}
                                  >
                                    <div className="flex space-x-3">
                                      <Avatar
                                        className={`${
                                          isReply ? "w-6 h-6" : "w-8 h-8"
                                        } border border-[#00F5FF]/30`}
                                      >
                                        <AvatarFallback
                                          className={`bg-[#0466C8] text-white ${
                                            isReply ? "text-xs" : "text-sm"
                                          }`}
                                        >
                                          {getUserInitials(
                                            null,
                                            comment.author_id
                                          )}
                                        </AvatarFallback>
                                      </Avatar>
                                      <div className="flex-1">
                                        <div className="bg-[var(--background)]/30 rounded-2xl px-3 py-2 inline-block max-w-full">
                                          <div className="flex items-center space-x-2 mb-1">
                                            <span
                                              className={`font-['Orbitron'] font-semibold text-[#00F5FF] ${
                                                isReply ? "text-xs" : "text-sm"
                                              }`}
                                            >
                                              {getUserDisplayName(
                                                null,
                                                comment.author_id
                                              )}
                                            </span>
                                          </div>
                                          <p
                                            className={`font-['Rajdhani'] text-white/90 ${
                                              isReply ? "text-xs" : "text-sm"
                                            } break-words`}
                                          >
                                            {comment.content}
                                          </p>
                                        </div>

                                        {/* Action buttons under comment bubble */}
                                        <div className="flex items-center space-x-4 mt-1 ml-3">
                                          <span className="text-white/40 text-xs font-['Rajdhani']">
                                            {new Date(
                                              comment.created_at
                                            ).toLocaleString()}
                                          </span>
                                          {isAuthenticated && !isReply && (
                                            <button
                                              onClick={() =>
                                                toggleReply(post.id, comment.id)
                                              }
                                              className="text-xs text-white/60 hover:text-[#00F5FF] transition-colors font-['Rajdhani'] font-semibold"
                                            >
                                              Reply
                                            </button>
                                          )}
                                        </div>

                                        {/* Reply Input - Facebook Style */}
                                        {comment.isReplying && (
                                          <div className="mt-3 ml-0">
                                            <div className="flex space-x-2 items-start">
                                              <Avatar className="w-8 h-8 border border-[#00F5FF]/30 flex-shrink-0">
                                                <AvatarFallback className="bg-[#0466C8] text-white text-sm">
                                                  {user?.id
                                                    ? getUserInitials(
                                                        null,
                                                        user.id
                                                      )
                                                    : "??"}
                                                </AvatarFallback>
                                              </Avatar>
                                              <div className="flex-1 flex items-end gap-2">
                                                <textarea
                                                  placeholder={`Write a reply to ${getUserDisplayName(
                                                    null,
                                                    comment.author_id
                                                  )}...`}
                                                  value={comment.newReply || ""}
                                                  onChange={(e) =>
                                                    updateReply(
                                                      post.id,
                                                      comment.id,
                                                      e.target.value
                                                    )
                                                  }
                                                  className="flex-1 bg-white text-gray-900 placeholder-gray-500 text-sm px-3 py-2 resize-none overflow-hidden border border-gray-300 rounded-2xl outline-none min-h-[36px]"
                                                  rows={1}
                                                  style={{
                                                    height: "auto",
                                                    minHeight: "36px",
                                                  }}
                                                  onInput={(e) => {
                                                    const target =
                                                      e.target as HTMLTextAreaElement;
                                                    target.style.height =
                                                      "auto";
                                                    target.style.height =
                                                      target.scrollHeight +
                                                      "px";
                                                  }}
                                                  onKeyPress={(e) => {
                                                    if (
                                                      e.key === "Enter" &&
                                                      !e.shiftKey &&
                                                      comment.newReply?.trim()
                                                    ) {
                                                      e.preventDefault();
                                                      handleAddReply(
                                                        post.id,
                                                        comment.id
                                                      );
                                                    }
                                                  }}
                                                />
                                                {comment.newReply?.trim() && (
                                                  <Button
                                                    size="sm"
                                                    onClick={() =>
                                                      handleAddReply(
                                                        post.id,
                                                        comment.id
                                                      )
                                                    }
                                                    className="bg-[#1877f2] hover:bg-[#166fe5] text-white rounded-full px-4 py-1 text-xs font-semibold h-7 mb-0.5"
                                                  >
                                                    Reply
                                                  </Button>
                                                )}
                                              </div>
                                            </div>
                                            <div className="flex justify-end mt-1 px-1">
                                              <button
                                                onClick={() =>
                                                  toggleReply(
                                                    post.id,
                                                    comment.id
                                                  )
                                                }
                                                className="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                                              >
                                                Cancel
                                              </button>
                                            </div>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
          ) : (
            <div className="text-center py-8">
              <p className="text-white/60 font-['Rajdhani']">
                No posts available.{" "}
                {isAuthenticated
                  ? "Be the first to create a post!"
                  : "Please log in to see posts."}
              </p>
            </div>
          )}
        </div>

        {/* Delete confirmation dialog */}
        <AlertDialog
          open={!!confirmDeleteId}
          onOpenChange={(open) => !open && setConfirmDeleteId(null)}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete post?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. Are you sure you want to delete
                this post?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setConfirmDeleteId(null)}>
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={() => {
                  if (confirmDeleteId) handleDeletePost(confirmDeleteId);
                  setConfirmDeleteId(null);
                }}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
};

export default Feed;
