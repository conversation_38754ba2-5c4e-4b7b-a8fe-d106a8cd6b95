import React, { useMemo, useState } from "react";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";

const Gallery = () => {
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [images, setImages] = useState<any[]>([]);

  const categories = useMemo(() => {
    const cats = new Set<string>();
    images.forEach((img) => {
      if (img.category) cats.add(String(img.category));
    });
    return ["all", ...Array.from(cats)];
  }, [images]);

  const filteredImages = useMemo(
    () =>
      selectedCategory === "all"
        ? images
        : images.filter((img) => String(img.category) === selectedCategory),
    [images, selectedCategory]
  );

  return (
    <Layout>
      <div className="page-content container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="font-['Orbitron'] text-4xl md:text-5xl font-bold text-[#00F5FF] mb-4">
            Lab <span className="text-[--text]">Gallery</span>
          </h1>
          <p className="text-xl font-['Rajdhani'] text-[--text-secondary] max-w-3xl mx-auto">
            Explore moments from our research journey, lab activities, and
            achievements
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-8">
          {categories.map((category) => (
            <Button
              key={category}
              onClick={() => setSelectedCategory(category)}
              variant={selectedCategory === category ? "default" : "outline"}
              className={`capitalize ${
                selectedCategory === category
                  ? "bg-[#0466C8] text-white"
                  : "border-[#00F5FF] text-[#00F5FF] hover:bg-[#00F5FF] hover:text-[#001233]"
              }`}
            >
              {category}
            </Button>
          ))}
        </div>

        {/* Gallery Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredImages.map((image) => (
            <div
              key={image.id}
              className="group relative overflow-hidden rounded-lg bg-[#001233]/60 backdrop-blur-sm border border-[#00F5FF]/20 hover:border-[#00F5FF]/40 transition-all duration-300"
            >
              <div className="aspect-square overflow-hidden">
                <img
                  src={image.url}
                  alt={image.title}
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                />
              </div>
              <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="absolute bottom-0 left-0 right-0 p-4">
                  <h3 className="font-['Orbitron'] text-white font-semibold mb-1">
                    {image.title}
                  </h3>
                  {image.description && (
                    <p className="font-['Rajdhani'] text-white/80 text-sm">
                      {image.description}
                    </p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* No Results */}
        {filteredImages.length === 0 && (
          <div className="text-center py-12">
            <h3 className="font-['Orbitron'] text-2xl text-white/70 mb-4">
              No images found
            </h3>
            <p className="font-['Rajdhani'] text-white/50">
              Try selecting a different category or add images from the Admin Dashboard.
            </p>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default Gallery;
