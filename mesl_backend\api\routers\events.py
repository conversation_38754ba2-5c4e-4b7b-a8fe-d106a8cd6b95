from typing import  List, Optional
from fastapi import APIRouter, HTTPException, File, UploadFile, Form
from pydantic import BaseModel
from sqlmodel import  select
from api.deps import  SessionDep, SupabaseDep, Admin
from models import Event, EventStatus
import uuid 
import logging 

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/events", tags=["Events"])

BUCKET_NAME = "event"

class EventResponse(BaseModel):
    id: uuid.UUID
    title: str
    datetime: str
    location: str
    description: str
    banner_url: str | None
    status: EventStatus

    class Config:
        from_attributes = True 


# class EventRequest(BaseModel):
#     title: str
#     datetime: str
#     location: str
#     description: str
#     banner_url: str | None 
#     status: EventStatus


# --- Helper Function ---

def get_path_from_url(public_url: str) -> Optional[str]:
    """Extracts the file path from a Supabase public URL."""
    try:
        # Splits the URL and takes the part after the bucket name
        return public_url.split(f"{BUCKET_NAME}/")[-1]
    except (IndexError, AttributeError):
        return None
    

# @router.post("/file")
# def test(
#     supabase: SupabaseDep,
#     file: UploadFile = File(...)
# ):
    
#     file_bytes = file.file.read()
#     # Use the underlying file object
#     res = supabase.storage.from_("feed").upload(
#         path=f"test/{file.filename}",   # name in bucket
#         file=file_bytes       # actual file object
#     )
#     print(res)
#     return {"message": "hoise"}


@router.post("/", response_model=Event)
async def create_event(
    session: SessionDep,
    supabase: SupabaseDep,
    user: Admin,
    # We use Form() instead of a Pydantic model to accept multipart/form-data
    title: str = Form(...),
    datetime: str = Form(...),
    location: str = Form(...),
    description: str = Form(...),
    banner: UploadFile = File(...) # The banner image is now a required file upload

):
    
    image_url = None
    if banner and banner.filename:
        # Create a unique file name to avoid collisions in the bucket
        file_ext = banner.filename.split(".")[-1]
        file_path = f"banners/{uuid.uuid4()}.{file_ext}"
        
        file_content = await banner.read()
        
        try:
            # Upload to Supabase Storage
            supabase.storage.from_(BUCKET_NAME).upload(
                path=file_path, file=file_content
        )
        except Exception as e:
            logger.error(f"Failed to upload file to Supabase: {e}")
            raise HTTPException(status_code=500, detail="Failed to upload file to Supabase storage")

        # Get the public URL for the uploaded file
        image_url = supabase.storage.from_(BUCKET_NAME).get_public_url(file_path)
    else:
        raise HTTPException(status_code=400, detail="Banner image is required.")

    create_event_model = Event(
        title=title,
        datetime=datetime,
        location=location,
        description=description,
        banner_url=image_url,
        status="upcoming",
    )

    session.add(create_event_model)
    session.commit()
    session.refresh(create_event_model)
    # The response is automatically constructed from the model, including the new fields
    return create_event_model

@router.get("/{event_id}", response_model=EventResponse)
async def get_event(event_id: uuid.UUID, session: SessionDep):
    event = session.get(Event, event_id)
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")

    return EventResponse(
        id=event.id,
        title=event.title,
        datetime=event.datetime,
        location=event.location,
        description=event.description,
        status=event.status,
        banner_url=event.banner_url
    )


@router.get("/", response_model=List[EventResponse])
async def list_events(session: SessionDep, status: Optional[EventStatus] = None):
    statement = select(Event)
    if status:
        statement = statement.where(Event.status == status)
    events = session.exec(statement).all()

    results = []
    for event in events:
        results.append(EventResponse(
            id=event.id,
            title=event.title,
            datetime=str(event.datetime) if event.datetime else "",  # Convert to string and handle None
            location=event.location,
            description=event.description,
            status=event.status,
            banner_url=event.banner_url
        ))

    return results


@router.put("/{event_id}", response_model=EventResponse)
async def update_event(
    event_id: uuid.UUID,
    session: SessionDep,
    supabase: SupabaseDep,
    user: Admin,
    # Use Form fields for updated data
    title: str = Form(...),
    datetime: str = Form(...),
    location: str = Form(...),
    description: str = Form(...),
    status: EventStatus = Form(...),
    # The new banner is optional. If not provided, the old one is kept.
    banner: Optional[UploadFile] = File(None)
):
    """
    Updates an event's details. Can optionally replace the banner image.
    """
    event = session.get(Event, event_id)
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")

    # Image update logic
    if banner and banner.filename:
        # If a new banner is uploaded, delete the old one first
        if event.banner_url:
            old_path = get_path_from_url(event.banner_url)
            if old_path:
                supabase.storage.from_(BUCKET_NAME).remove([old_path])
        
        # Upload the new banner
        file_ext = banner.filename.split(".")[-1]
        new_path = f"banners/{uuid.uuid4()}.{file_ext}"
        file_content = await banner.read()
        
        try:
            # Upload to Supabase Storage
            supabase.storage.from_(BUCKET_NAME).upload(
                path=new_path, file=file_content
        )
        except Exception as e:
            logger.error(f"Failed to upload file to Supabase: {e}")
            raise HTTPException(status_code=500, detail="Failed to upload file to Supabase storage")

        event.banner_url = supabase.storage.from_(BUCKET_NAME).get_public_url(new_path)

    # Update text-based fields
    event.title = title
    event.description = description
    event.datetime = datetime
    event.location = location
    event.status = status

    session.add(event)
    session.commit()
    session.refresh(event)
    return event


@router.delete("/{event_id}", status_code=204)
async def delete_event(
    event_id: uuid.UUID, 
    session: SessionDep, 
    supabase: SupabaseDep, 
    user: Admin
):
    """
    Deletes an event and its associated banner image from storage.
    """
    event = session.get(Event, event_id)
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")
    
    # --- Delete image from Supabase Storage ---
    # >>>>>> DO IT MANUALLY <<<<<<< (developers are also human)

    # Delete the event from the database
    session.delete(event)
    session.commit()
    
    return {"message": "Event and associated banner deleted successfully"}


# @router.post("/{event_id}/upload-image")
# async def upload_event_image(
#     event_id: str,
#     session: SessionDep,
#     supabase: SupabaseDep,
#     file: UploadFile = File(None)
# ):
#     event = session.get(Event, event_id)
#     if not event:
#         raise HTTPException(status_code=404, detail="Event not found")
#     if file and file.filename != "":
#         image_filename = f"test/{event.title}_{file.filename}"
#         file_content = await file.read()
#         response = supabase.storage.from_(BUCKET_NAME).upload(
#             image_filename, file_content
#         )
#         if response.status_code == 200:
#             image_url = supabase.storage.from_(BUCKET_NAME).get_public_url(image_filename)

#     return image_url
    # try:
    #     # Validate file type (basic check)
    #     if not file.content_type.startswith("image/"):
    #         raise HTTPException(status_code=400, detail="Only image uploads are allowed")

    #     # Create a unique file name
    #     file_ext = file.filename.split(".")[-1]
    #     unique_name = f"{event_id}/{uuid.uuid4()}.{file_ext}"

    #     # Read file content
    #     file_bytes = await file.read()

    #     # Upload to Supabase Storage
    #     res = supabase.storage.from_(BUCKET_NAME).upload(unique_name, file_bytes)

    #     if res.get("error"):
    #         raise HTTPException(status_code=500, detail=res["error"]["message"])

    #     # Get public URL (or create signed URL if private)
    #     public_url = supabase.storage.from_(BUCKET_NAME).get_public_url(unique_name)

    #     # TODO: Save to your DB table (EventImages) here
    #     # db.save_event_image(event_id, unique_name, public_url)

    #     return {
    #         "message": "Image uploaded successfully",
    #         "file_path": unique_name,
    #         "public_url": public_url
    #     }

    # except Exception as e:
    #     raise HTTPException(status_code=500, detail=str(e))