# MESL Website - React Vite Tailwind

## Mechatronics & Embedded System Lab

A modern, responsive website for the Mechatronics & Embedded System Lab built with React, Vite, and Tailwind CSS.

## Features

- **Modern React Architecture**: Built with React 18 and TypeScript for type safety
- **Fast Development**: Powered by Vite for lightning-fast development and builds
- **Responsive Design**: Fully responsive design using Tailwind CSS
- **Interactive Components**: Dynamic features including project filtering, social feed, and gallery
- **Authentication**: Integrated with Supabase for user authentication
- **Smooth Animations**: Custom animations and transitions for enhanced user experience

## Pages

- **Home**: Hero section, featured projects, leadership insights, and upcoming events
- **Projects**: Filterable project showcase with search and category filters
- **Feed**: Social media-style feed for lab updates and interactions
- **Gallery**: Image gallery with category filtering
- **Team**: Leadership, research teams, and student profiles

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn

### Installation

```sh
# Clone the repository
git clone <repository-url>

# Navigate to the project directory
cd MESL_website_sj

# Install dependencies
npm install

# Start the development server
npm run dev
```

The application will be available at `http://localhost:8080`

### Build for Production

```sh
# Build the project
npm run build

# Preview the production build
npm run preview
```

## Technologies Used

This project is built with modern web technologies:

- **React 18** - Modern React with hooks and functional components
- **TypeScript** - Type-safe JavaScript for better development experience
- **Vite** - Fast build tool and development server
- **Tailwind CSS** - Utility-first CSS framework for rapid styling
- **Shadcn/ui** - High-quality, accessible UI components
- **React Router** - Client-side routing for single-page application
- **React Icons** - Popular icon library for React
- **Supabase** - Backend-as-a-Service for authentication and database

## Project Structure

```
src/
├── components/          # Reusable React components
│   ├── Layout.tsx      # Main layout wrapper
│   ├── Navbar.tsx      # Navigation component
│   ├── Footer.tsx      # Footer component
│   └── ui/             # Shadcn/ui components
├── pages/              # Page components
│   ├── Index.tsx       # Home page
│   ├── Projects.tsx    # Projects page
│   ├── Feed.tsx        # Social feed page
│   ├── Gallery.tsx     # Image gallery page
│   └── Team.tsx        # Team page
├── lib/                # Utility functions
├── hooks/              # Custom React hooks
└── integrations/       # Third-party integrations
```

## Key Features

### Interactive Elements

- Search and filter functionality for projects
- Like/unlike posts in the social feed
- Category filtering for gallery images
- Responsive mobile navigation
- Theme toggle (dark/light mode)

### Animations

- Scroll-triggered animations using Intersection Observer
- Custom CSS animations (glitch effect, floating elements)
- Smooth transitions and hover effects

### Responsive Design

- Mobile-first approach
- Optimized for all screen sizes
- Touch-friendly interface

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contact

Mechatronics & Embedded System Lab

- Email: <EMAIL>
- Facebook: [MESLMEC](https://www.facebook.com/MESLMEC)
- LinkedIn: [Mechatronics & Embedded System Lab](https://www.linkedin.com/company/mechatronics-embedded-system-lab/)
