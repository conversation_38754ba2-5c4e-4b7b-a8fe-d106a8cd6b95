# Usage

## Environment Variables

Required environment variables are defined in `.env.example` file. Copy it to `.env` and fill in the values.

## Initial Admin User Setup

Run the following command to create an initial admin user:

```bash
python prestart.py
```

# Api Guide

## Authentication

### Get access token

> POST `/auth/access-token`

Authenticate a user

- `access_token` is valid for 1 hour.
- `refresh_token` can be used to get a new access token when the current one expires. Never expires but can only be used once.
- Use the access token in the `Authorization` header for API requests.
- STORE THIS TOKEN IN FRONTEND CLIENT

#### Request Body

| Field    | Type   |
| -------- | ------ |
| email    | string |
| password | string |

#### Response

```json
{
  "access_token": "string",
  "token_type": "bearer",
  "expires_in": 3600,
  "expires_at": **********,
  "refresh_token": "string"
}
```

### Refresh Token

> POST `/auth/refresh-token`

Get a new access token using a refresh token.

#### Request Body

| Field      | Type   |
| ---------- | ------ |
| refresh_token | string |

#### Response

```json
{
  "access_token": "string",
  "token_type": "bearer",
  "expires_in": 3600,
  "expires_at": **********,
  "refresh_token": "string"
}
```

### Invite User

> POST `/auth/invite`

Create a new user account. only admins can invite users.

#### Request Body

```json
{
  "email": "string"
}
```

will send a joining link. upon clicking that link it will then redirect user to `http://SITEURL/#access_token=<access_token>&expires_at=<epoch_time>&expires_in=<in_seconds>&refresh_token=<refresh_token>&token_type=bearer&type=invite`
build and store the token and redirect the user to a signup page. and send appropriate informations to the api backend to submit signup application.

### Signup User

> POST `/auth/accept-invite`

accept an invite and create a new user account and set user's password.

#### Request Body

```json
{
  "department": "string",
  "full_name": "string",
  "username": "string",
  "password": "string"
}
```

## Topics

### Create Topic

> POST `/topics/`
> Create a new topic.

#### Request Body

```json
{
  "name": "string"
}
```

#### Response

```json
{
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "name": "string"
}
```

### Get All Topics

> GET `/topics/`
> Retrieve all topics.

#### Response

```json
[
  {
    "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "name": "string"
  }
]
```

## Events

### Event Creation

> POST `/events/`

---


Creates a new event with an image banner.

#### Request Body (multipart/form-data)

| Field       | Type           | Description                               |
| ----------- | -------------- | ----------------------------------------- |
| title       | string         | Event title                               |
| datetime    | string         | Event datetime in ISO format              |
| location    | string         | Event location                            |
| description | string         | Event description                         |
| banner      | file           | Image file for event banner (required)    |

#### Response

```json
{
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "title": "string",
  "datetime": "2025-10-24T10:00:00",
  "location": "string",
  "description": "string",
  "banner_url": "string",
  "status": "upcoming"
}
```

Note: The event will be automatically set to "upcoming" status when created.

---

### Particular Event

> GET `/events/{event-id}`

- event-id must resemble "id" from _POST_

#### Response

```json
{
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "title": "string",
  "datetime": "10:00 AM 23 feb",
  "location": "string",
  "description": "string",
  "status": "upcoming",
  "banner_url": "str"
}
```

---

### Filter Events

#### **options**

_1. upcoming_  
_2. ongoing_  
_3. archived_

> GET `/events/?status=options`

---

### Update Events

> PUT `/events/{event-id}`

Update an event's details and optionally its banner image.

#### Request Body (multipart/form-data)

| Field       | Type           | Description                               |
| ----------- | -------------- | ----------------------------------------- |
| title       | string         | Event title                               |
| datetime    | string         | Event datetime in ISO format              |
| location    | string         | Event location                            |
| description | string         | Event description                         |
| status      | string         | Event status (upcoming/ongoing/archived)  |
| banner      | file           | Optional new banner image                 |

#### Response

Returns the updated event object with all fields including the banner URL if updated.

### Event Topics Saga

> /events/{event-id}

_this endpoint allows you to update the topics of an event. You can add or remove topics as needed._

#### Request Body

```json
{
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "title": "string",
  "datetime": "10:00 AM 23 feb",
  "location": "string",
  "description": "string",
  "status": "upcoming",
  "banner_url": "str"
}
```
---

### Delete Event

> DELETE `/events/{event-id}`

Delete an event and its associated banner image.

Status Code: 204 (No Content)

Note: The associated banner image in storage must be deleted manually by the developer.

---

## Project

### Create Project

> POST `/project/`

Create a new project.

#### Request Body

| Field         | Type              | Description                  |
| ------------- | ----------------- | ---------------------------- |
| overview      | string            | Project overview             |
| status        | string            | Project status (enum)        |
| title         | string            | Project title                |
| proposed_date | datetime          | Proposal date (ISO format)   |
| start_date    | datetime\|null    | Start date (optional)        |
| end_date      | datetime\|null    | End date (optional)          |
| members       | array(UUID)\|null | List of user IDs (optional)  |
| topics        | array(UUID)\|null | List of topic IDs (optional) |

#### Response

Returns the created project ID.

```json
{
  "id": "uuid"
}
```

---

### Get Project

> GET `/project/{project_id}`

Retrieve a project by its ID.

#### Response

Returns the project details.

---

### Get All Projects

> GET `/project/`

Retrieve all projects.

#### Response

Returns a list of all projects.

---

### Update Project Details

> PATCH `/project/{project_id}`

Update project details (partial update).

#### Request Body

| Field         | Type           | Description              |
| ------------- | -------------- | ------------------------ |
| overview      | string\|null   | Project overview         |
| status        | string\|null   | Project status (enum)    |
| title         | string\|null   | Project title            |
| proposed_date | datetime\|null | Proposal date (optional) |
| start_date    | datetime\|null | Start date (optional)    |
| end_date      | datetime\|null | End date (optional)      |

#### Response

Returns the updated project object.

---

### Add Members to Project

> PUT `/project/{project_id}/members`

Add members to a project.

#### Request Body

| Field   | Type        | Description      |
| ------- | ----------- | ---------------- |
| members | array(UUID) | List of user IDs |

#### Response

```json
{
  "message": "Members added successfully"
}
```

---

### Remove Members from Project

> DELETE `/project/{project_id}/members`

Remove members from a project.

#### Request Body

| Field   | Type        | Description      |
| ------- | ----------- | ---------------- |
| members | array(UUID) | List of user IDs |

#### Response

```json
{
  "message": "Members removed successfully"
}
```

---

### Add Topics to Project

> PUT `/project/{project_id}/topics`

Add topics to a project.

#### Request Body

| Field  | Type        | Description       |
| ------ | ----------- | ----------------- |
| topics | array(UUID) | List of topic IDs |

#### Response

```json
{
  "message": "Topics added successfully"
}
```

---

### Remove Topics from Project

> DELETE `/project/{project_id}/topics`

Remove topics from a project.

#### Request Body

| Field  | Type        | Description       |
| ------ | ----------- | ----------------- |
| topics | array(UUID) | List of topic IDs |

#### Response

```json
{
  "message": "Topics removed successfully"
}
```

---

### Delete Project

> DELETE `/project/{project_id}`

Delete a project by its ID.

#### Response

```json
{
  "message": "Project deleted successfully"
}
```

---

# Feed

| key      | value               |
| -------- | ------------------- |
| all id's | uuid                |
| post     | string(<= 1000 )    |
| comment  | string(<= 500 char) |

### Create Post

> POST `/posts/`

**input**

```
{
  "content": "string"
}
```

**reponse**

```
{
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "author_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "content": "string",
  "created_at": "2025-08-01T04:08:46.866Z",
  "likes_count": 0
}
```

### Read newsfeed (all posts)

> GET `/posts/`

**response**

```
[
  {
    "id": "eec3c634-b2cc-40f5-a55a-13b593e28677",
    "author_id": "9338ed34-2776-40dc-934b-0d7992de394f",
    "content": "i am samir",
    "created_at": "2025-07-31T16:27:42.047421",
    "likes_count": 0,
    "comments": [
      {
        "id": "34c40b66-8e77-4bb9-8210-f4ff5de9602e",
        "author_id": "0068243c-bfbe-4188-82aa-523e02383819",
        "content": "super se bhi uper",
        "created_at": "2025-07-31T19:57:44.489519",
        "parent_comment_id": null,
        "child_comments": [
          {
            "id": "fc55c6fe-d840-4f27-9d8e-4ea1b9f61dab",
            "author_id": "9338ed34-2776-40dc-934b-0d7992de394f",
            "content": "dunno",
            "created_at": "2025-07-31T20:18:54.460002",
            "parent_comment_id": "34c40b66-8e77-4bb9-8210-f4ff5de9602e",
            "child_comments": []
          }
        ]
      }
    ]
  },
  {
    "id": "0028bcb7-a339-4746-979d-4d8d96f3482d",
    "author_id": "0068243c-bfbe-4188-82aa-523e02383819",
    "content": "liked deja",
    "created_at": "2025-07-31T16:21:33.104394",
    "likes_count": 1,
    "comments": []
  }
]
```

### Update Post

> PUT `/posts/{post_id}`

---

- [x] tracking if a post is updated or not

**input**

- [ ] post id

```
{
  "content": "string"
}
```

**reponse**

```
{
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "author_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "content": "string",
  "created_at": "2025-08-01T04:08:46.866Z",
  "likes_count": 0
}
```

### Delete Post

> DELETE `/posts/{post_id}`

**input**

- [ ] post id

**response**

```
post deleted successfully
```

### Like Post

> POST `/posts/{post_id}/like`

you don't have the power to unlike a post (why do you wanna do it anyway?)

**input**

- [ ] post id

### Create Comment

> POST `/posts/{post_id}/comments`

**input**

- [ ] post id

```
{
  "content": "string",
  "parent_comment_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6"
}
```

> acutal response structure

```
{
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "author_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "content": "string",
  "created_at": "2025-08-01T05:23:41.959Z",
  "parent_comment_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "post_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6"
}
```

#### How comment id's work?

imagine araf posted on fb. sajib commented **nice**

- content = nice
- parent_comment_id = null
  > this comment will generate a response body like below

**response**

```
{
  "id": "sajib-cmnt",
  "author_id": "sajib",
  "content": "nice",
  "created_at": "2025-08-01T05:23:41.959Z",
  "parent_comment_id": "null",
  "post_id": "araf-post"
}
```

now jubaer replied to sajib's comment **iron sajib**

- content = iron sajib
- parent_comment_id = **the id got from sajib's comment**

> his response would generate

```
{
  "id": "jubaer-cmnt",
  "author_id": "abcd-12",
  "content": "iron sajib",
  "created_at": "2025-08-01T05:25:41.959Z",
  "parent_comment_id": "sajib-cmnt",
  "post_id": "araf-post"
}
```

### Update Comment

> PUT `/posts/{comment_id}/comments`

---

- [x] tracking if a comment is updated or not

**input**

- [ ] comment id

```
{
  "content": "string",
}
```

**response**

```
{
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "author_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "content": "string",
  "created_at": "2025-08-01T05:23:41.959Z",
  "parent_comment_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "post_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6"
}
```

### Delete Comment

> DELETE `/posts/{comment_id}/comments`

**input**

- [ ] comment id

**response**

> successful

```
comment deleted successfully
```

# TODO

- [ ] admin power
- [x] event image storing
- [x] event topics
- [ ] remove topics from everywhere
- [x] event create (image support)

## user management

- [ ] activate user
- [ ] make signup invite only

## project

- [ ] check if member/topic exists before adding

## feed

- [x] if user update a post, change the update time and flag 'update'?
- [x] same with comments, make is_updated=true/false & updated_time
- [x] user could upload media files
- [ ] make read_feed optimized
- [ ] like comment (V 2.0)
