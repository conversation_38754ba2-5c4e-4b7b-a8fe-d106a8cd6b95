import logging
import uuid
from typing import Annotated

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPEx<PERSON>
from fastapi.security import OAuth2PasswordRequestForm

from api.deps import CurrentUser, SessionDep, SupabaseDep, TokenDep
from models import Token, User, UserSignup, UserStatus

router = APIRouter(prefix="/auth", tags=["auth"])


@router.post("/access-token")
def login(
    supabase: SupabaseDep,
    form_data: Annotated[OAuth2PasswordRequestForm, Depends()],
) -> Token:
    try:
        auth_response = supabase.auth.sign_in_with_password(
            {"email": form_data.username, "password": form_data.password}
        )
        session = auth_response.session
        if not session or not session.access_token:
            raise HTTPException(
                status_code=401,
                detail="Invalid credentials",
            )
        return Token(
            access_token=session.access_token,
            expires_in=session.expires_in,
            expires_at=session.expires_at,
            refresh_token=session.refresh_token,
        )
    except Exception as e:
        logging.error(f"Login error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="An error occurred during login. Please try again later.",
        )


@router.post("/refresh-token")
def refresh_token(supabase: SupabaseDep, refresh_token: str) -> Token:
    try:
        refresh_response = supabase.auth.refresh_session(refresh_token)
        session = refresh_response.session
        if not session or not session.access_token:
            raise HTTPException(
                status_code=401,
                detail="Invalid token",
            )
        return Token(
            access_token=session.access_token,
            expires_in=session.expires_in,
            expires_at=session.expires_at,
            refresh_token=session.refresh_token,
        )
    except Exception as e:
        logging.error(f"Refresh token error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="An error occurred while refreshing the token. Please try again later.",
        )


@router.post("/invite")
def invite_user(supabase: SupabaseDep, user: CurrentUser, email: str):
    if user.is_admin is False:
        raise HTTPException(
            status_code=403,
            detail="Only admins can invite users.",
        )

    try:
        response = supabase.auth.admin.invite_user_by_email(
            email, {"data": {"did_set_password": False}}
        )
        if not response or not response.user:
            raise HTTPException(
                status_code=400,
                detail="Failed to send invite. Please check the email address.",
            )
        return response
    except Exception as e2:
        logging.error(f"Invite error: {str(e2)}")
        raise HTTPException(
            status_code=500,
            detail="An error occurred while sending the invite. Please try again later.",
        )


@router.post("/accept-invite")
def accept_invite(
    db: SessionDep, supabase: SupabaseDep, token: TokenDep, form_data: UserSignup
):
    u = supabase.auth.get_user(token)
    if not u or not u.user:
        raise HTTPException(
            status_code=401,
            detail="Invalid token or user not found.",
        )

    try:
        db.get_one(User, uuid.UUID(u.user.id))
        return {"message": "User already exists."}
    except Exception:
        pass

    did_set_password = u.user.user_metadata.get("did_set_password", False)
    if did_set_password:
        return {"message": "User already set password."}

    try:
        supabase.auth.admin.update_user_by_id(
            u.user.id,
            {
                "password": form_data.password,
                "user_metadata": {"did_set_password": True},
            },
        )
    except Exception as e2:
        logging.error(f"Error updating user password: {str(e2)}")
        raise HTTPException(
            status_code=500,
            detail="An error occurred while updating the user password. Please try again later.",
        )

    user = User(
        id=uuid.UUID(u.user.id),
        username=form_data.username,
        full_name=form_data.full_name,
        status=UserStatus.active,
        department=form_data.department,
    )

    try:
        db.add(user)
        db.commit()
        db.refresh(user)
    except Exception as e3:
        logging.error(f"Error adding user to database: {str(e3)}")
        raise HTTPException(
            status_code=500,
            detail="An error occurred while saving the user. Please try again later.",
        )

    return {"message": "Invite accepted successfully."}
