import { useState, useEffect } from "react";
import { createPortal } from "react-dom";
import { X, <PERSON>, EyeOff, Mail, Lock, User } from "lucide-react";
import { API_BASE_URL } from "@/lib/env";

interface LoginModalProps {
  children: React.ReactNode;
  onLoginSuccess?: (userData: any) => void;
  initialMode?: "login" | "signup";
}

interface FormData {
  fullName: string;
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

interface FormErrors {
  fullName?: string;
  username?: string;
  email?: string;
  password?: string;
  confirmPassword?: string;
  general?: string;
}

const LoginModal: React.FC<LoginModalProps> = ({
  children,
  onLoginSuccess,
  initialMode = "login",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isSignupMode, setIsSignupMode] = useState(initialMode === "signup");
  const [formData, setFormData] = useState<FormData>({
    fullName: "",
    username: "",
    email: "",
    password: "",
    confirmPassword: "",
  });
  const [errors, setErrors] = useState<FormErrors>({});

  // Handle escape key press
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isOpen) {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      document.body.style.overflow = "hidden"; // Prevent background scrolling
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);

  const handleInputChange =
    (field: keyof FormData) => (e: React.ChangeEvent<HTMLInputElement>) => {
      setFormData((prev) => ({
        ...prev,
        [field]: e.target.value,
      }));

      // Clear field-specific error when user starts typing
      if (errors[field]) {
        setErrors((prev) => ({
          ...prev,
          [field]: undefined,
        }));
      }
    };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Signup-specific validations
    if (isSignupMode) {
      if (!formData.fullName.trim()) {
        newErrors.fullName = "Full name is required";
      }

      if (!formData.username.trim()) {
        newErrors.username = "Username is required";
      } else if (formData.username.length < 3) {
        newErrors.username = "Username must be at least 3 characters";
      } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
        newErrors.username =
          "Username can only contain letters, numbers, and underscores";
      }

      if (!formData.confirmPassword) {
        newErrors.confirmPassword = "Please confirm your password";
      } else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = "Passwords do not match";
      }
    }

    // Common validations
    if (!formData.email) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    if (!formData.password) {
      newErrors.password = "Password is required";
    } else if (formData.password.length < 6) {
      newErrors.password = "Password must be at least 6 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (isLoading) return; // Prevent double submission

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      if (isSignupMode) {
        // TODO: Implement proper signup with Supabase if needed
        throw new Error(
          "Signup is not available yet. Please contact an admin for an invite."
        );
      }

      // Use backend authentication endpoint with x-www-form-urlencoded as expected by OAuth2PasswordRequestForm
      const params = new URLSearchParams();
      params.append("username", formData.email);
      params.append("password", formData.password);

      const response = await fetch(`${API_BASE_URL}/auth/access-token`, {
        method: "POST",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        body: params,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.detail || "Login failed. Please check your credentials."
        );
      }

      const tokenData = await response.json();
      console.log(
        "LoginModal: Token received:",
        tokenData.access_token.substring(0, 50) + "..."
      );

      if (!tokenData.access_token) {
        throw new Error("Login failed. No access token received.");
      }

      // Store the token (same as debug function)
      sessionStorage.setItem("access_token", tokenData.access_token);
      if (tokenData.refresh_token) {
        sessionStorage.setItem("refresh_token", tokenData.refresh_token);
      }
      console.log("LoginModal: Token stored in sessionStorage");

      // Get user info using the new /users/me endpoint
      let userData;
      try {
        const userResponse = await fetch(`${API_BASE_URL}/users/me`, {
          headers: {
            Authorization: `Bearer ${tokenData.access_token}`,
          },
        });

        if (userResponse.ok) {
          const userInfo = await userResponse.json();
          userData = {
            id: userInfo.id,
            email: userInfo.email || formData.email,
            name: userInfo.full_name || formData.email.split("@")[0],
            username: userInfo.username || formData.email.split("@")[0],
            role: userInfo.is_admin ? "admin" : "user",
          };
        } else {
          throw new Error("Failed to get user info");
        }
      } catch (userError) {
        console.warn(
          "Failed to get user info from /users/me, using fallback:",
          userError
        );
        // Fallback user data
        userData = {
          id: formData.email,
          email: formData.email,
          name: formData.email.split("@")[0],
          username: formData.email.split("@")[0],
          role: "user",
        };
      }

      if (onLoginSuccess) {
        onLoginSuccess(userData);
      }

      // Reset form and close modal
      setFormData({
        fullName: "",
        username: "",
        email: "",
        password: "",
        confirmPassword: "",
      });
      setIsOpen(false);
      setIsSignupMode(initialMode === "signup"); // Reset to initial mode

      // Show success message (you can replace this with a toast notification)
      console.log("Login successful!");
    } catch (error: any) {
      setErrors({
        general:
          error.message ||
          `${isSignupMode ? "Signup" : "Login"} failed. Please try again.`,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setIsOpen(false);
    setFormData({
      fullName: "",
      username: "",
      email: "",
      password: "",
      confirmPassword: "",
    });
    setErrors({});
    setIsSignupMode(initialMode === "signup"); // Reset to initial mode
    setShowPassword(false);
    setShowConfirmPassword(false);
    setIsLoading(false);
  };

  const switchMode = () => {
    setIsSignupMode(!isSignupMode);
    setErrors({});
    setFormData({
      fullName: "",
      username: "",
      email: "",
      password: "",
      confirmPassword: "",
    });
    setShowPassword(false);
    setShowConfirmPassword(false);
  };

  const handleTriggerClick = () => {
    setIsSignupMode(initialMode === "signup");
    setIsOpen(true);
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    // Only close if clicking the backdrop, not the modal content
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  const handleContentClick = (e: React.MouseEvent) => {
    // Prevent backdrop click when clicking inside modal content
    e.stopPropagation();
  };

  return (
    <>
      {/* Trigger Element */}
      <div onClick={handleTriggerClick} style={{ display: "inline-block" }}>
        {children}
      </div>

      {/* Modal */}
      {isOpen &&
        createPortal(
          <div
            className={`modal ${isOpen ? "active" : ""}`}
            onClick={handleBackdropClick}
          >
            <div className="modal-content" onClick={handleContentClick}>
              {/* Custom close button to match the theme */}
              <button
                className="close-modal"
                onClick={handleClose}
                aria-label="Close modal"
                disabled={isLoading}
              >
                <X className="h-5 w-5" />
              </button>

              <h2>
                {isSignupMode ? "Join" : "Login to"}{" "}
                <span className="neon-text">ME</span>SL
              </h2>

              <form onSubmit={handleSubmit}>
                {errors.general && (
                  <div className="bg-red-500/10 border border-red-500/20 text-red-500 px-4 py-3 rounded-md text-sm mb-4">
                    {errors.general}
                  </div>
                )}

                {/* Signup-specific fields */}
                {isSignupMode && (
                  <>
                    <div className="form-group">
                      <label htmlFor="signup-fullname">Full Name</label>
                      <div className="relative">
                        <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <input
                          type="text"
                          id="signup-fullname"
                          value={formData.fullName}
                          onChange={handleInputChange("fullName")}
                          className={`pl-10 ${errors.fullName ? "error" : ""}`}
                          placeholder="Enter your full name"
                          disabled={isLoading}
                          required
                        />
                      </div>
                      {errors.fullName && (
                        <span className="error-message">{errors.fullName}</span>
                      )}
                    </div>

                    <div className="form-group">
                      <label htmlFor="signup-username">Username</label>
                      <div className="relative">
                        <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <input
                          type="text"
                          id="signup-username"
                          value={formData.username}
                          onChange={handleInputChange("username")}
                          className={`pl-10 ${errors.username ? "error" : ""}`}
                          placeholder="Choose a username"
                          disabled={isLoading}
                          required
                        />
                      </div>
                      {errors.username && (
                        <span className="error-message">{errors.username}</span>
                      )}
                    </div>
                  </>
                )}

                <div className="form-group">
                  <label htmlFor="login-email">Email</label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <input
                      type="email"
                      id="login-email"
                      value={formData.email}
                      onChange={handleInputChange("email")}
                      className={`pl-10 ${errors.email ? "error" : ""}`}
                      placeholder="Enter your email"
                      disabled={isLoading}
                      required
                    />
                  </div>
                  {errors.email && (
                    <span className="error-message">{errors.email}</span>
                  )}
                </div>

                <div className="form-group">
                  <label htmlFor="login-password">Password</label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <input
                      type={showPassword ? "text" : "password"}
                      id="login-password"
                      value={formData.password}
                      onChange={handleInputChange("password")}
                      className={`pl-10 pr-12 ${
                        errors.password ? "error" : ""
                      }`}
                      placeholder="Enter your password"
                      disabled={isLoading}
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors bg-transparent border-none cursor-pointer p-1"
                      disabled={isLoading}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                  {errors.password && (
                    <span className="error-message">{errors.password}</span>
                  )}
                </div>

                {/* Confirm Password for signup */}
                {isSignupMode && (
                  <div className="form-group">
                    <label htmlFor="signup-confirm-password">
                      Confirm Password
                    </label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <input
                        type={showConfirmPassword ? "text" : "password"}
                        id="signup-confirm-password"
                        value={formData.confirmPassword}
                        onChange={handleInputChange("confirmPassword")}
                        className={`pl-10 pr-12 ${
                          errors.confirmPassword ? "error" : ""
                        }`}
                        placeholder="Confirm your password"
                        disabled={isLoading}
                        required
                      />
                      <button
                        type="button"
                        onClick={() =>
                          setShowConfirmPassword(!showConfirmPassword)
                        }
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors bg-transparent border-none cursor-pointer p-1"
                        disabled={isLoading}
                      >
                        {showConfirmPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </button>
                    </div>
                    {errors.confirmPassword && (
                      <span className="error-message">
                        {errors.confirmPassword}
                      </span>
                    )}
                  </div>
                )}

                <button
                  type="submit"
                  className="btn-primary btn-block"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <div className="flex items-center gap-2 justify-center">
                      <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                      {isSignupMode ? "Creating Account..." : "Logging in..."}
                    </div>
                  ) : isSignupMode ? (
                    "Create Account"
                  ) : (
                    "Login"
                  )}
                </button>
              </form>

              <p className="form-note">
                {isSignupMode
                  ? "Already have an account?"
                  : "Don't have an account?"}{" "}
                <button
                  type="button"
                  className="link-button"
                  onClick={switchMode}
                  disabled={isLoading}
                >
                  {isSignupMode ? "Login here" : "Sign up here"}
                </button>
              </p>
            </div>
          </div>,
          document.body
        )}
    </>
  );
};

export default LoginModal;
