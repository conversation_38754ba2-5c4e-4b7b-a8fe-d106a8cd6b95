// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://xoqgkagozetucryyvazr.supabase.co";
// TODO: Replace with your Supabase project's anon key from Project Settings > API > Project API keys > anon public
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhvcWdrYWdvemV0dWNyeXl2YXpyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYxODA5MTMsImV4cCI6MjA3MTc1NjkxM30.1-ws2yr7N6jlgkvKtsCrLcm9AQ9cWjUbkQBK56gJ8Z0";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);