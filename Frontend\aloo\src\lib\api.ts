import { API_BASE_URL } from "./env";

export type EventStatus = "upcoming" | "ongoing" | "archived" | string;

export type EventTopic = {
  id: string;
  name?: string;
};

export type EventResponse = {
  id: string;
  title: string;
  date: string; // ISO date
  time: string; // HH:mm:ss
  location: string;
  description: string;
  detailedDescription?: string;
  learningOutcomes?: string;
  prerequisites?: string;
  image?: string;
  type?: string;
  status: EventStatus;
  capacity?: number;
  registeredCount?: number;
  organizer?: {
    name: string;
    role: string;
    email: string;
    phone: string;
    avatar: string;
  };
  agenda?: {
    time: string;
    activity: string;
  }[];
  topics: EventTopic[];
  attendees: { user: UserResponse }[];
};

export type CreateEventRequest = {
  title: string;
  date: string; // YYYY-MM-DD
  time: string; // HH:mm:ss
  location: string;
  description: string;
  status: EventStatus;
  topics?: string[] | null; // list of topic UUIDs
};

async function handle<T>(input: Promise<Response> | Response): Promise<T> {
  const r = await input;
  if (!r.ok) {
    const text = await r.text().catch(() => "");
    throw new Error(`API ${r.status}: ${text || r.statusText}`);
  }

  // Handle empty responses (204 No Content)
  if (r.status === 204 || r.headers.get("content-length") === "0") {
    return undefined as T;
  }

  return r.json() as Promise<T>;
}

async function getValidToken(): Promise<string | null> {
  if (typeof window === "undefined") return null;

  const token = sessionStorage.getItem("access_token");
  if (!token) return null;

  // Check if token is expired by trying to decode it (basic check)
  try {
    const payload = JSON.parse(atob(token.split(".")[1]));
    const currentTime = Math.floor(Date.now() / 1000);

    // If token expires in less than 5 minutes, try to refresh
    if (payload.exp && payload.exp - currentTime < 300) {
      const refreshToken = sessionStorage.getItem("refresh_token");
      if (refreshToken) {
        try {
          const refreshResult = await authApi.refresh(refreshToken);
          return refreshResult.access_token;
        } catch (error) {
          console.error("Token refresh failed:", error);
          // Clear invalid tokens
          sessionStorage.removeItem("access_token");
          sessionStorage.removeItem("refresh_token");
          return null;
        }
      }
    }

    return token;
  } catch (error) {
    // If token is malformed, return it anyway (let backend handle it)
    return token;
  }
}

function authHeaders(): Record<string, string> {
  const token =
    typeof window !== "undefined"
      ? sessionStorage.getItem("access_token")
      : null;
  return token ? { Authorization: `Bearer ${token}` } : {};
}

async function authHeadersAsync(): Promise<Record<string, string>> {
  const token = await getValidToken();
  return token ? { Authorization: `Bearer ${token}` } : {};
}

export const eventsApi = {
  async list(status?: string): Promise<EventResponse[]> {
    const url = new URL("/events/", API_BASE_URL);
    if (status) url.searchParams.set("status", status);
    return handle(fetch(url.toString()));
  },
  async get(id: string): Promise<EventResponse> {
    const url = new URL(`/events/${id}`, API_BASE_URL);
    return handle(fetch(url.toString()));
  },
  async create(body: CreateEventRequest): Promise<EventResponse> {
    const url = new URL("/events/", API_BASE_URL);
    return handle(
      fetch(url.toString(), {
        method: "POST",
        headers: { "Content-Type": "application/json", ...authHeaders() },
        body: JSON.stringify(body),
      })
    );
  },
  async update(
    id: string,
    body: Partial<CreateEventRequest>
  ): Promise<EventResponse> {
    const url = new URL(`/events/${id}`, API_BASE_URL);
    return handle(
      fetch(url.toString(), {
        method: "PUT",
        headers: { "Content-Type": "application/json", ...authHeaders() },
        body: JSON.stringify(body),
      })
    );
  },
  async remove(id: string): Promise<{ message: string }> {
    const url = new URL(`/events/${id}`, API_BASE_URL);
    return handle(
      fetch(url.toString(), {
        method: "DELETE",
        headers: { ...authHeaders() },
      })
    );
  },

  async register(id: string): Promise<any> {
    const url = new URL(`/events/${id}/register`, API_BASE_URL);
    return handle(fetch(url.toString(), { method: "POST" }));
  },

  async unregister(id: string): Promise<any> {
    const url = new URL(`/events/${id}/unregister`, API_BASE_URL);
    return handle(fetch(url.toString(), { method: "POST" }));
  },
};

// -------------------- Projects API --------------------
export type ProjectStatus = string;
export type ProjectResponse = {
  id: string;
  title: string;
  overview: string;
  status: ProjectStatus;
  proposed_date: string; // ISO
  start_date?: string | null;
  end_date?: string | null;
  // backend model may include relations; keep them optional/unknown
  members?: unknown;
  topics?: unknown;
};

export type ProjectCreateRequest = {
  title: string;
  overview: string;
  status: ProjectStatus;
  proposed_date?: string; // ISO
  start_date?: string | null;
  end_date?: string | null;
  members?: string[] | null; // user IDs
  topics?: string[] | null; // topic IDs
};

export const projectsApi = {
  async remove(id: string): Promise<{ message: string }> {
    const url = new URL(`/project/${id}`, API_BASE_URL);
    return handle(
      fetch(url.toString(), {
        method: "DELETE",
        headers: { ...authHeaders() },
      })
    );
  },
  async addMembers(
    id: string,
    members: string[]
  ): Promise<{ message: string }> {
    const url = new URL(`/project/${id}/members`, API_BASE_URL);
    return handle(
      fetch(url.toString(), {
        method: "PUT",
        headers: { "Content-Type": "application/json", ...authHeaders() },
        body: JSON.stringify(members),
      })
    );
  },
  async removeMembers(
    id: string,
    members: string[]
  ): Promise<{ message: string }> {
    const url = new URL(`/project/${id}/members`, API_BASE_URL);
    return handle(
      fetch(url.toString(), {
        method: "DELETE",
        headers: { "Content-Type": "application/json", ...authHeaders() },
        body: JSON.stringify(members),
      })
    );
  },
  async addTopics(id: string, topics: string[]): Promise<{ message: string }> {
    const url = new URL(`/project/${id}/topics`, API_BASE_URL);
    return handle(
      fetch(url.toString(), {
        method: "PUT",
        headers: { "Content-Type": "application/json", ...authHeaders() },
        body: JSON.stringify(topics),
      })
    );
  },
  async removeTopics(
    id: string,
    topics: string[]
  ): Promise<{ message: string }> {
    const url = new URL(`/project/${id}/topics`, API_BASE_URL);
    return handle(
      fetch(url.toString(), {
        method: "DELETE",
        headers: { "Content-Type": "application/json", ...authHeaders() },
        body: JSON.stringify(topics),
      })
    );
  },
  async list(): Promise<ProjectResponse[]> {
    const url = new URL("/project/", API_BASE_URL);
    return handle(fetch(url.toString()));
  },
  async get(id: string): Promise<ProjectResponse> {
    const url = new URL(`/project/${id}`, API_BASE_URL);
    return handle(fetch(url.toString()));
  },
  async create(
    body: ProjectCreateRequest
  ): Promise<string | { id: string } | any> {
    const url = new URL("/project/", API_BASE_URL);
    return handle(
      fetch(url.toString(), {
        method: "POST",
        headers: { "Content-Type": "application/json", ...authHeaders() },
        body: JSON.stringify(body),
      })
    );
  },
  async update(
    id: string,
    body: Partial<ProjectCreateRequest>
  ): Promise<ProjectResponse> {
    const url = new URL(`/project/${id}`, API_BASE_URL);
    return handle(
      fetch(url.toString(), {
        method: "PATCH",
        headers: { "Content-Type": "application/json", ...authHeaders() },
        body: JSON.stringify(body),
      })
    );
  },
};

// -------------------- Topics API --------------------
export type Topic = {
  id: string;
  name: string;
};

export const topicsApi = {
  async list(): Promise<Topic[]> {
    const url = new URL("/topics/", API_BASE_URL);
    return handle(fetch(url.toString()));
  },
  async create(name: string): Promise<Topic> {
    const url = new URL("/topics/", API_BASE_URL);
    return handle(
      fetch(url.toString(), {
        method: "POST",
        headers: { "Content-Type": "application/json", ...authHeaders() },
        body: JSON.stringify({ name }),
      })
    );
  },
  async remove(id: string): Promise<Topic | { message: string }> {
    const url = new URL(`/topics/${id}`, API_BASE_URL);
    return handle(
      fetch(url.toString(), {
        method: "DELETE",
        headers: { ...authHeaders() },
      })
    );
  },
};

// -------------------- Users API --------------------
export type UserResponse = {
  id: string;
  username: string;
  full_name?: string | null;
  status?: string;
  department?: string | null;
};

export const usersApi = {
  async list(): Promise<UserResponse[]> {
    const url = new URL("/users/", API_BASE_URL);
    return handle(fetch(url.toString()));
  },
  async getTotalCount(): Promise<{ total: number }> {
    const url = new URL("/users/count", API_BASE_URL);
    return handle(
      fetch(url.toString(), {
        method: "GET",
        headers: { ...authHeaders() },
      })
    );
  },
};

// -------------------- Auth API --------------------

export const authApi = {
  async login(formData: FormData): Promise<any> {
    const url = new URL("/auth/access-token", API_BASE_URL);
    return handle(
      fetch(url.toString(), {
        method: "POST",
        body: formData,
      })
    );
  },
  async validateToken(): Promise<boolean> {
    try {
      const token = await getValidToken();
      if (!token) return false;

      // Try a simple API call to validate token
      const url = new URL("/posts/", API_BASE_URL);
      const response = await fetch(url.toString(), {
        method: "GET",
        headers: { Authorization: `Bearer ${token}` },
      });

      return response.ok;
    } catch (error) {
      console.error("Token validation failed:", error);
      return false;
    }
  },
  async refresh(refresh_token: string): Promise<any> {
    const url = new URL("/auth/refresh-token", API_BASE_URL);
    const result = await handle<any>(
      fetch(url.toString(), {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ refresh_token }),
      })
    );
    try {
      if (result?.access_token)
        sessionStorage.setItem("access_token", result.access_token);
      if (result?.refresh_token)
        sessionStorage.setItem("refresh_token", result.refresh_token);
    } catch {}
    return result;
  },
  async invite(email: string): Promise<any> {
    const url = new URL("/auth/invite", API_BASE_URL);
    return handle(
      fetch(url.toString(), {
        method: "POST",
        headers: { "Content-Type": "application/json", ...authHeaders() },
        body: JSON.stringify({ email }),
      })
    );
  },
  async acceptInvite(formData: FormData): Promise<any> {
    const url = new URL("/auth/accept-invite", API_BASE_URL);
    return handle(
      fetch(url.toString(), {
        method: "POST",
        body: formData,
      })
    );
  },
};

// -------------------- Posts (Feed) API --------------------
export type CommentRead = {
  id: string;
  author_id: string;
  content: string;
  created_at: string; // ISO
  parent_comment_id?: string | null;
  child_comments?: CommentRead[];
};

export type PostRead = {
  id: string;
  author: {
    id: string;
    username: string;
    full_name?: string;
  };
  content: string;
  created_at: string; // ISO
  updated_at: string; // ISO
  is_updated: boolean;
  likes_count: number;
  comments?: CommentRead[];
  images?: any[];
};

export type PostCreate = {
  content: string;
  images?: File[];
};

export type CommentCreate = {
  content: string;
  parent_comment_id?: string | null;
};

export const postsApi = {
  async create(body: PostCreate): Promise<PostRead> {
    const url = new URL("/posts/", API_BASE_URL);

    // Use FormData for multipart/form-data when images are included
    const formData = new FormData();
    formData.append("content", body.content);

    if (body.images && body.images.length > 0) {
      body.images.forEach((image) => {
        formData.append("images", image);
      });
    }

    const headers = await authHeadersAsync();
    return handle(
      fetch(url.toString(), {
        method: "POST",
        headers: { ...headers }, // Don't set Content-Type for FormData
        body: formData,
      })
    );
  },
  async update(
    id: string,
    body: { content?: string; images?: File[]; remove_image_ids?: string[] }
  ): Promise<PostRead> {
    const url = new URL(`/posts/${id}`, API_BASE_URL);
    const formData = new FormData();
    if (body.content !== undefined) formData.append("content", body.content);
    if (body.images) {
      for (const file of body.images) {
        formData.append("images", file);
      }
    }
    if (body.remove_image_ids) {
      for (const rid of body.remove_image_ids) {
        formData.append("remove_image_ids", rid);
      }
    }

    const headers = await authHeadersAsync();
    return handle(
      fetch(url.toString(), {
        method: "PUT",
        headers: { ...headers },
        body: formData,
      })
    );
  },
  async remove(id: string): Promise<{ message: string }> {
    const url = new URL(`/posts/${id}`, API_BASE_URL);
    return handle(
      fetch(url.toString(), {
        method: "DELETE",
        headers: { ...authHeaders() },
      })
    );
  },
  async like(id: string): Promise<void> {
    const url = new URL(`/posts/${id}/like`, API_BASE_URL);
    const headers = await authHeadersAsync();
    return handle(
      fetch(url.toString(), {
        method: "POST",
        headers: { ...headers },
      })
    );
  },
  async comment(id: string, body: CommentCreate): Promise<CommentRead> {
    const url = new URL(`/posts/${id}/comments`, API_BASE_URL);
    return handle(
      fetch(url.toString(), {
        method: "POST",
        headers: { "Content-Type": "application/json", ...authHeaders() },
        body: JSON.stringify(body),
      })
    );
  },
  async updateComment(
    id: string,
    body: { content: string }
  ): Promise<CommentRead> {
    const url = new URL(`/posts/${id}/comments`, API_BASE_URL);
    return handle(
      fetch(url.toString(), {
        method: "PUT",
        headers: { "Content-Type": "application/json", ...authHeaders() },
        body: JSON.stringify(body),
      })
    );
  },
  async removeComment(id: string): Promise<{ message: string }> {
    const url = new URL(`/posts/${id}/comments`, API_BASE_URL);
    return handle(
      fetch(url.toString(), {
        method: "DELETE",
        headers: { ...authHeaders() },
      })
    );
  },
  async list(params?: {
    limit?: number;
    offset?: number;
  }): Promise<PostRead[]> {
    const url = new URL("/posts/", API_BASE_URL);
    if (params?.limit != null)
      url.searchParams.set("limit", String(params.limit));
    if (params?.offset != null)
      url.searchParams.set("offset", String(params.offset));
    return handle(fetch(url.toString()));
  },
};
