import { useEffect, useState, useMemo } from "react";
import { use<PERSON>ara<PERSON>, Link } from "react-router-dom";
import Layout from "@/components/Layout";
import EventMediaUploader from "@/components/EventMediaUploader";
import { eventsApi, UserResponse } from "@/lib/api";
import { useAuth } from "@/hooks/useAuth";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  ArrowLeft,
  Calendar,
  MapPin,
  Users,
  Clock,
  User,
  Mail,
  Phone,
  CheckCircle,
  Heart,
  AlertTriangle,
} from "lucide-react";

const EventDetail = () => {
  const { id } = useParams();
  const [isInterested, setIsInterested] = useState(false);
  const { isAuthenticated, user } = useAuth();
  const [attendees, setAttendees] = useState<UserResponse[]>([]);

  const [event, setEvent] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const isRegistered = useMemo(() => {
    if (!user || !attendees) return false;
    return attendees.some((attendee) => attendee.id === user.id);
  }, [attendees, user]);

  useEffect(() => {
    let active = true;
    if (!id) {
      setEvent(null);
      setLoading(false);
      return;
    }
    const fetchEvent = async () => {
      try {
        const eventData = await eventsApi.get(id);
        if (active) {
          setEvent(eventData);
          setAttendees(eventData.attendees.map((a: any) => a.user) || []);
        }
      } catch (err) {
        if (active) {
          setError("Unable to load this event right now. Please try again later.");
          setEvent(null);
        }
      } finally {
        if (active) {
          setLoading(false);
        }
      }
    };

    fetchEvent();

    return () => {
      active = false;
    };
  }, [id]);

  if (loading) {
    return (
      <Layout>
        <div className="page-content container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="font-['Orbitron'] text-2xl font-bold text-[#00F5FF] mb-4">
              Loading event...
            </h1>
          </div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="page-content container mx-auto px-4 py-8">
          <div className="text-center">
            <div className="inline-flex items-center gap-2 px-4 py-3 rounded-md border border-red-500/30 bg-red-500/10 text-red-300">
              <AlertTriangle className="h-5 w-5" />
              <span className="font-medium">{error}</span>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (!event) {
    return (
      <Layout>
        <div className="page-content container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="font-['Orbitron'] text-4xl font-bold text-[#00F5FF] mb-4">
              Event Not Found
            </h1>
            <p className="text-[var(--text-secondary)] mb-8">
              The requested event could not be found.
            </p>
            <Link to="/">
              <Button className="bg-[var(--accent)] text-[var(--text)] hover:bg-[var(--secondary)] hover:text-[var(--text)] transition-colors">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Home
              </Button>
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  const handleInterested = () => {
    setIsInterested(!isInterested);
  };

  const handleRegister = async () => {
    if (!id || !isAuthenticated) {
      // Optionally prompt to login
      setError("You must be logged in to register.");
      return;
    }

    const originalAttendees = attendees;
    // Optimistic update
    if (user) {
      const newUserResponse: UserResponse = {
        id: user.id,
        username: user.email, // Assuming email can be used as username
        full_name: user.name,
      };
      setAttendees([...attendees, newUserResponse]);
    }

    try {
      await eventsApi.register(id);
    } catch (err) {
      setError("Failed to register for the event. Please try again.");
      // Revert on failure
      setAttendees(originalAttendees);
    }
  };

  const handleUnregister = async () => {
    if (!id || !isAuthenticated) return;

    const originalAttendees = attendees;
    // Optimistic update
    setAttendees(attendees.filter(a => a.id !== user?.id));

    try {
      await eventsApi.unregister(id);
    } catch (err) {
      setError("Failed to unregister from the event. Please try again.");
      // Revert on failure
      setAttendees(originalAttendees);
    }
  };

  return (
    <Layout>
      <div className="page-content container mx-auto px-4 py-8">
        {/* Back Button */}
        <div className="mb-8">
          <Link to="/">
            <Button
              variant="outline"
              className="border-[#00F5FF] text-[#00F5FF] hover:bg-[#00F5FF] hover:text-[#001233]"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Home
            </Button>
          </Link>
        </div>

        {/* Event Header */}
        <div className="mb-12">
          <div className="flex flex-wrap items-center gap-2 mb-4">
            <span
              className={`px-3 py-1 rounded-full text-sm font-medium ${
                event.type === "Workshop"
                  ? "bg-purple-500/20 text-purple-400 border border-purple-500/30"
                  : event.type === "Seminar"
                  ? "bg-orange-500/20 text-orange-400 border border-orange-500/30"
                  : "bg-green-500/20 text-green-400 border border-green-500/30"
              }`}
            >
              {event.type}
            </span>
            <span className="px-3 py-1 rounded-full text-sm font-medium bg-[#0466C8]/20 text-[#00F5FF] border border-[#0466C8]/30">
              {event.status}
            </span>
          </div>

          <h1 className="font-['Orbitron'] text-4xl md:text-5xl font-bold text-[#00F5FF] mb-6">
            {event.title}
          </h1>

          <p className="text-xl font-['Rajdhani'] text-[var(--text-secondary)] leading-relaxed max-w-4xl">
            {event.description}
          </p>
        </div>

        {/* Event Image */}
        <div className="mb-12">
          <div className="relative overflow-hidden rounded-xl border border-[#00F5FF]/20">
            <img
              src={event.image}
              alt={event.title}
              className="w-full h-96 object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-[#001233]/60 to-transparent"></div>
          </div>
        </div>

        {/* Event Details Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Event Details */}
            <Card className="project-detail-card">
              <CardHeader>
                <h2 className="font-['Orbitron'] text-2xl font-bold text-[#00F5FF]">
                  Event Details
                </h2>
              </CardHeader>
              <CardContent className="space-y-4 text-[var(--text-secondary)] font-['Rajdhani'] text-lg leading-relaxed">
                <p>{event.detailedDescription}</p>
                <p>
                  <strong>What you'll learn:</strong> {event.learningOutcomes}
                </p>
                <p>
                  <strong>Prerequisites:</strong> {event.prerequisites}
                </p>
              </CardContent>
            </Card>

            {/* Agenda */}
            {event.agenda && (
              <Card className="project-detail-card">
                <CardHeader>
                  <h2 className="font-['Orbitron'] text-2xl font-bold text-[#00F5FF]">
                    Event Agenda
                  </h2>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {event.agenda.map((item, index) => (
                      <div
                        key={index}
                        className="flex items-start space-x-4 p-4 bg-[var(--background)]/50 rounded-lg"
                      >
                        <Clock className="h-5 w-5 text-[#00F5FF] mt-1" />
                        <div>
                          <p className="font-semibold text-[var(--text)]">
                            {item.time}
                          </p>
                          <p className="text-[var(--text-secondary)]">
                            {item.activity}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Admin-only media manager */}
            {isAuthenticated && user?.role === "admin" && id && (
              <EventMediaUploader eventId={id!} />
            )}
            {/* Event Info */}
            <Card className="project-detail-card">
              <CardHeader>
                <h3 className="font-['Orbitron'] text-xl font-bold text-[#00F5FF]">
                  Event Information
                </h3>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center text-[var(--text-secondary)]">
                  <Calendar className="mr-3 h-5 w-5 text-[#00F5FF]" />
                  <div>
                    <p className="font-medium">Date & Time</p>
                    <p className="text-sm">{event.date}</p>
                    <p className="text-sm">{event.time}</p>
                  </div>
                </div>
                <div className="flex items-center text-[var(--text-secondary)]">
                  <MapPin className="mr-3 h-5 w-5 text-[#00F5FF]" />
                  <div>
                    <p className="font-medium">Location</p>
                    <p className="text-sm">{event.location}</p>
                  </div>
                </div>
                <div className="flex items-center text-[var(--text-secondary)]">
                  <Users className="mr-3 h-5 w-5 text-[#00F5FF]" />
                  <div>
                    <p className="font-medium">Capacity</p>
                    <p className="text-sm">
                      {event.registeredCount}/{event.capacity} registered
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Organizer Info */}
            {event.organizer && (
              <Card className="project-detail-card">
                <CardHeader>
                  <h3 className="font-['Orbitron'] text-xl font-bold text-[#00F5FF]">
                    Event Organizer
                  </h3>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <img
                      src={event.organizer.avatar}
                      alt={event.organizer.name}
                      className="w-12 h-12 rounded-full border border-[#00F5FF]/30"
                    />
                    <div>
                      <p className="font-medium text-[var(--text)]">
                        {event.organizer.name}
                      </p>
                      <p className="text-sm text-[var(--text-secondary)]">
                        {event.organizer.role}
                      </p>
                    </div>
                  </div>
                  <div className="space-y-2 text-sm text-[var(--text-secondary)]">
                    <div className="flex items-center">
                      <Mail className="mr-2 h-4 w-4 text-[#00F5FF]" />
                      {event.organizer.email}
                    </div>
                    <div className="flex items-center">
                      <Phone className="mr-2 h-4 w-4 text-[#00F5FF]" />
                      {event.organizer.phone}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Action Buttons */}
            {isAuthenticated && (
              <Card className="project-detail-card">
                <CardHeader>
                  <h3 className="font-['Orbitron'] text-xl font-bold text-[#00F5FF]">
                    Join This Event
                  </h3>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Button
                    onClick={isRegistered ? handleUnregister : handleRegister}
                    className={`w-full transition-colors ${
                      isRegistered
                        ? "bg-red-500 hover:bg-red-600 text-white"
                        : "bg-[var(--accent)] text-[var(--text)] hover:bg-[var(--secondary)] hover:text-[var(--text)]"
                    }`}
                  >
                    {isRegistered ? (
                      <>
                        <CheckCircle className="mr-2 h-4 w-4" />
                        Unregister
                      </>
                    ) : (
                      <>
                        <User className="mr-2 h-4 w-4" />
                        Register for Event
                      </>
                    )}
                  </Button>

                  {isRegistered && (
                    <p className="text-sm text-green-400 text-center">
                      You are registered for this event.
                    </p>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Attendees List */}
            <Card className="project-detail-card">
              <CardHeader>
                <h3 className="font-['Orbitron'] text-xl font-bold text-[#00F5FF]">
                  Attendees ({attendees.length})
                </h3>
              </CardHeader>
              <CardContent className="space-y-3">
                {attendees.length > 0 ? (
                  attendees.map((attendee) => (
                    <div key={attendee.id} className="flex items-center space-x-3">
                      <div>
                        <p className="font-medium text-white">
                          {attendee.full_name || attendee.username}
                        </p>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-white/60">No one has registered yet.</p>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  );
};


export default EventDetail;
