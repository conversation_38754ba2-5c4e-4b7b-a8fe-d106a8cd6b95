from fastapi import APIRouter, HTTPException, status, UploadFile, File, Form
from sqlmodel import select
from typing import List,Optional
from datetime import datetime 
from pydantic import BaseModel, Field 
from models import Post, Comment, PostLike, PostImage, User
from api.deps import SessionDep, CurrentUser, SupabaseDep
from sqlalchemy.orm import selectinload
import uuid
import logging 

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/posts", tags=["posts"])


#
# ── POST SCHEMAS ───────────────────────────────────────────────────────────────
#
class PostAuthorRead(BaseModel):
    id: uuid.UUID
    username: str
    full_name: str

    class Config:
        from_attributes = True

class PostImageRead(BaseModel):
    id: uuid.UUID
    file_url: str

    class Config:
        from_attributes = True


class PostRead(BaseModel):
    id: uuid.UUID
    author: PostAuthorRead
    content: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    is_updated: Optional[bool] = False
    likes_count: int

    comments: Optional[List["CommentRead"]] = []
    images: Optional[List[PostImageRead]] = []

    class Config:
        from_attributes = True


#
# ── COMMENT SCHEMAS ────────────────────────────────────────────────────────────
class CommentCreate(BaseModel):
    content: str = Field(..., max_length=500)
    parent_comment_id: Optional[uuid.UUID] = None


class CommentUpdate(BaseModel):
    content: str = Field(..., max_length=500)


class CommentRead(BaseModel):
    id: uuid.UUID
    author_id: uuid.UUID
    content: str
    created_at: datetime
    parent_comment_id: Optional[uuid.UUID]

    # nested replies
    child_comments: Optional[List["CommentRead"]] = []

    class Config:
        from_attributes = True


# this is needed for the forward refs in Pydantic
CommentRead.model_rebuild()
PostRead.model_rebuild()



@router.post("/", response_model=PostRead, status_code=status.HTTP_201_CREATED)
def create_post(
    session: SessionDep,
    supabase: SupabaseDep,
    current_user: CurrentUser,
    content: str = Form(...),
    images: Optional[List[UploadFile]] = File(None, description="Optional images for the post"),
):
    """
    Create a new post with optional images.
    """
    now = datetime.now()
    post = Post(
        author_id=current_user.id,
        content=content,
        is_updated=False,
        created_at=now,
        updated_at=now
    )
    session.add(post)
    session.commit()
    session.refresh(post)

    if images:
        image_urls = []
        for image in images:
            if not image.filename:
                continue
            
            # Create a unique path for the image in storage
            file_path = f"{current_user.id}/{uuid.uuid4()}-{image.filename}"

            try:
                data = image.file.read()
                # Rewind file pointer after reading
                image.file.seek(0)
                supabase.storage.from_("feed").upload(
                    path=file_path,
                    file=data,
                    file_options={"content-type": image.content_type},
                )
                public_url = supabase.storage.from_("feed").get_public_url(file_path)
                image_urls.append(public_url)
                
                # Create PostImage record
                post_image = PostImage(post_id=post.id, file_url=public_url)
                session.add(post_image)

            except Exception as e:
                # Rollback transaction on failure
                session.rollback()
                logger.error(f"Failed to upload image {image.filename}: {e}")
                raise HTTPException(status_code=500, detail=f"Failed to upload image: {image.filename}")

    session.commit()
    session.refresh(post)
    # Eagerly load author for the response
    post_with_author = session.exec(
        select(Post).where(Post.id == post.id).options(selectinload(Post.author))
    ).one()
    return post_with_author



@router.put("/{post_id}", response_model=PostRead)
def update_post(
    post_id: uuid.UUID,
    session: SessionDep,
    current_user: CurrentUser,
    supabase: SupabaseDep,
    content: Optional[str] = Form(None),
    images: Optional[List[UploadFile]] = File(None, description="New images to add"),
    remove_image_ids: Optional[List[uuid.UUID]] = Form(None, description="IDs of existing images to remove"),
):
    """
    Update an existing post. Allows updating content, adding new images, and removing existing ones.
    """
    post = session.get(Post, post_id, options=[selectinload(Post.images)])
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")
    if post.author_id != current_user.id:
        raise HTTPException(status_code=403, detail="You are not authorized for this activity")

    post_updated = False
    if content is not None:
        post.content = content
        post_updated = True

    # Remove old images if requested
    if remove_image_ids:
        images_to_remove = [img for img in post.images if img.id in remove_image_ids]
        paths_to_remove_from_storage = []
        for img in images_to_remove:
            # Extract path from URL for deletion from Supabase
            path = "/".join(img.file_url.split("/")[-2:])
            paths_to_remove_from_storage.append(path)
            session.delete(img)
        
        if paths_to_remove_from_storage:
            try:
                supabase.storage.from_("feed").remove(paths_to_remove_from_storage)
            except Exception as e:
                logger.warning(f"Failed to delete some images from storage: {e}")
        post_updated = True


    # Add new images
    if images:
        for image in images:
            if not image.filename:
                continue
            file_path = f"{current_user.id}/{uuid.uuid4()}-{image.filename}"
            try:
                data = image.file.read()
                image.file.seek(0)
                supabase.storage.from_("feed").upload(
                    path=file_path,
                    file=data,
                    file_options={"content-type": image.content_type},
                )
                public_url = supabase.storage.from_("feed").get_public_url(file_path)
                post_image = PostImage(post_id=post.id, file_url=public_url)
                session.add(post_image)
                post_updated = True
            except Exception as e:
                session.rollback()
                logger.error(f"Failed to upload new image during update: {e}")
                raise HTTPException(status_code=500, detail="Failed to upload a new image.")

    if post_updated:
        post.updated_at = datetime.now()
        post.is_updated = True
        session.add(post)
        session.commit()
        session.refresh(post)

    return post

    
@router.delete("/{post_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_post(post_id:uuid.UUID, session:SessionDep, current_user:CurrentUser, supabase: SupabaseDep):
    post = session.get(Post, post_id, options=[selectinload(Post.images)])
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")
    if post.author_id != current_user.id:
        raise HTTPException(status_code=403, detail="You are not authorized for this activity")
    
    # Delete images from Supabase Storage
    if post.images:
        paths_to_remove = ["/".join(img.file_url.split("/")[-2:]) for img in post.images]
        try:
            supabase.storage.from_("feed").remove(paths_to_remove)
        except Exception as e:
            logger.error(f"Could not delete images from storage for post {post_id}: {e}")
            # Decide if you want to stop the deletion process or just log the error
            # For now, we'll proceed with deleting DB records

    session.delete(post)
    session.commit()
    return {"message":"Post deleted successfully"}



@router.post("/{post_id}/like", status_code=status.HTTP_204_NO_CONTENT)
def like_post(post_id: uuid.UUID, session: SessionDep, current_user: CurrentUser):
    # 1) Make sure the post exists 
    post = session.get(Post, post_id)
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")
    
    
    # 2) Prevent duplicate likes
    existing_like = session.exec(
        select(PostLike)
        .where(PostLike.post_id == post_id)
        .where(PostLike.user_id == current_user.id)
    ).first()

    if existing_like:
        # Unlike the post
        session.delete(existing_like)
        post.likes_count = max(0, post.likes_count - 1)
        session.add(post)
        session.commit()
        return {"message": "Post unliked"}


    # 3) Create the like record
    like = PostLike(post_id=post_id, user_id=current_user.id)
    session.add(like)

    # 4) Increment the cached count
    post.likes_count += 1
    session.add(post)

    session.commit()
    return {"message": "Post liked"}



@router.post("/{post_id}/comments", response_model=Comment, status_code=status.HTTP_201_CREATED)
def comment_on_post(post_id:uuid.UUID, comment_in:CommentCreate, session:SessionDep, current_user:CurrentUser):
    # Validate post exists
    post = session.get(Post, post_id)
    if not post:
        raise HTTPException(404, "Post not found")

    # Optionally enforce parent_comment validity
    if comment_in.parent_comment_id:
        parent = session.get(Comment, comment_in.parent_comment_id)
        if not parent or parent.post_id != post_id:
            raise HTTPException(400, "Invalid parent comment")

    comment = Comment(
        author_id=current_user.id,
        post_id=post_id,
        content=comment_in.content,
        created_at=datetime.now(),
        parent_comment_id=comment_in.parent_comment_id,
    )
    session.add(comment)
    session.commit()
    session.refresh(comment)
    return comment



@router.put("/comments/{comment_id}", response_model=Comment)
def update_comment(comment_id:uuid.UUID, comment_in:CommentUpdate, session:SessionDep, current_user:CurrentUser):
    comment = session.get(Comment, comment_id)
    if not comment:
        raise HTTPException(status_code=404, detail="comment not found")
    if comment.author_id != current_user.id:
        raise HTTPException(status_code=403, detail="You are not authorized for this activity")
    
    comment.content = comment_in.content 
    comment.updated_at = datetime.now()
    comment.is_updated = True

    session.add(comment)
    session.commit()
    session.refresh(comment)
    return comment



@router.delete("/comments/{comment_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_comment(comment_id:uuid.UUID, session:SessionDep, current_user:CurrentUser):   
    comment = session.get(Comment, comment_id)
    if not comment:
        raise HTTPException(status_code=404, detail="Comment not found")

    if comment.author_id != current_user.id:
        raise HTTPException(status_code=403, detail="You are not authorized for this activity")
    
    # Deleting child comments will depend on your database's cascade settings.
    # If ON DELETE CASCADE is set, you only need to delete the parent.
    # Otherwise, you must delete children first.
    
    session.delete(comment)
    session.commit()
    return {"message":"Comment successfully deleted"}



@router.get("/", response_model=List[PostRead])
def read_feed(*, limit: int = 20, offset: int = 0, session: SessionDep):
    """
    Fetch the main feed of posts.
    """
    posts = session.exec(
        select(Post)
        .order_by(Post.created_at.desc())
        .offset(offset)
        .limit(limit)
        .options(
            selectinload(Post.author), # Eagerly load the author info
            selectinload(Post.images), # Eagerly load the images
            selectinload(Post.comments).selectinload(Comment.child_comments),
        )
    ).all()

    for post in posts:
        # Set defaults for posts that might be missing these fields
        if post.updated_at is None:
            post.updated_at = post.created_at
        if not isinstance(post.is_updated, bool):
            post.is_updated = False
        # Filter to show only top-level comments
        post.comments = [
            c for c in post.comments
            if c.parent_comment_id is None
        ]

    return posts