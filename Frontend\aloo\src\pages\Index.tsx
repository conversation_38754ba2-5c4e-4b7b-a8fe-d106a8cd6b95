import { useEffect, useMemo, useState } from "react";
import { Link } from "react-router-dom";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { Calendar, MapPin, Users } from "lucide-react";

const Index = () => {
  const [visibleElements, setVisibleElements] = useState(new Set<string>());

  // Data sources (to be replaced later with backend fetch)
  const [projects, setProjects] = useState<any[]>([]);
  const [events, setEvents] = useState<any[]>([]);

  // Featured projects: pick first 3 (prefer Active first if available)
  const featuredProjects = useMemo(() => {
    if (!projects || projects.length === 0) return [] as any[];
    const active = projects.filter((p) => String(p.status || "") === "Active");
    const rest = projects.filter((p) => String(p.status || "") !== "Active");
    return [...active, ...rest].slice(0, 3);
  }, [projects]);

  // Upcoming events: status === 'upcoming', first 3
  const upcomingEvents = useMemo(() => {
    if (!events || events.length === 0) return [] as any[];
    return events
      .filter(
        (e) => String((e.status || "").toLowerCase()) === "upcoming"
      )
      .slice(0, 3);
  }, [events]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const elementId = entry.target.getAttribute("data-animate");
          if (elementId) {
            if (entry.isIntersecting) {
              setVisibleElements((prev) => new Set(prev).add(elementId));
            } else {
              setVisibleElements((prev) => {
                const newSet = new Set(prev);
                newSet.delete(elementId);
                return newSet;
              });
            }
          }
        });
      },
      {
        threshold: Array.from({ length: 101 }, (_, i) => i / 100),
      }
    );

    const elementsToObserve = document.querySelectorAll("[data-animate]");
    elementsToObserve.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  return (
    <Layout>
      {/* Hero Section */}
      <section className="hero">
        <div className="container">
          <div className="hero-grid">
            <div className="hero-content">
              <h1
                className="glitch"
                data-text="Mechatronics & Embedded System Lab"
              >
                Mechatronics & Embedded System Lab
              </h1>
              <p>
                Pioneering the future through advanced research and innovative
                technologies.
              </p>
              <div className="hero-buttons">
                <Link to="/projects">
                  <button className="btn-primary">Explore Projects</button>
                </Link>
                <Link to="/team">
                  <button className="btn-secondary">Join Our Team</button>
                </Link>
              </div>

              {/* Trusted By */}
              <div className="hero-trusted">
                <span className="label">Trusted by</span>
                <div className="logos">
                  <img src="/images/Footer logos/MEC.png" alt="MEC" />
                  <img src="/images/Footer logos/DuLogo-removebg-preview.png" alt="DU" />
                  <img src="/images/Footer logos/BangladeshLogo-removebg-preview.png" alt="Gov" />
                </div>
              </div>
            </div>
            <div className="hero-logo-wrap">
              <img
                className="hero-logo"
                src="/images/Footer logos/mesl-logo.png"
                alt="mesl-logo"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Welcome Section */}
      <section className="welcome-section">
        <div className="container">
          <div className="section-header">
            <h2>
              Why Choose <span className="neon-text">MESL</span>
            </h2>
            <p>Perspectives from our lab's leadership team</p>
          </div>
          <div className="welcome-flex">
            <div className="welcome-box">
              <img
                src="https://images.unsplash.com/photo-1581092795360-fd1ca04f0952?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="Innovation"
                className="welcome-img"
              />
              <h3>Launch Your Ideas</h3>
              <p>
                Bring your innovative concepts to life with our cutting-edge
                resources and expert mentorship.
              </p>
            </div>
            <div className="welcome-box">
              <img
                src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="Community"
                className="welcome-img"
              />
              <h3>Collaborate & Grow</h3>
              <p>
                Join a vibrant community of passionate engineers, researchers,
                and creators working together.
              </p>
            </div>
            <div className="welcome-box">
              <img
                src="https://images.unsplash.com/photo-1559136555-9303baea8ebd?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="Impact"
                className="welcome-img"
              />
              <h3>Make an Impact</h3>
              <p>
                Contribute to real-world projects that solve meaningful
                challenges and shape the future.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Projects Section */}
      <section className="featured-projects-section py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="font-['Orbitron'] text-4xl md:text-5xl font-bold text-[#00F5FF] mb-4">
              Featured <span className="text-[var(--text)]">Projects</span>
            </h2>
            <p className="text-xl font-['Rajdhani'] text-[var(--text-secondary)] max-w-3xl mx-auto">
              Discover our most innovative and impactful research projects
            </p>
          </div>

          {featuredProjects.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
              {featuredProjects.map((project) => (
                <div
                  key={project.id}
                  className="project-detail-card overflow-hidden hover:border-[var(--secondary)]/60 transition-all duration-300 group"
                >
                  <div className="h-48 overflow-hidden">
                    <img
                      src={project.image || project.hero_image}
                      alt={project.title}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-6">
                    <div className="flex flex-wrap gap-2 mb-3">
                      {project.status && (
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium ${
                            project.status === "Active"
                              ? "bg-green-500/20 text-green-400 border border-green-500/30"
                              : "bg-blue-500/20 text-blue-400 border border-blue-500/30"
                          }`}
                        >
                          {project.status}
                        </span>
                      )}
                      {project.category && (
                        <span className="px-2 py-1 rounded-full text-xs font-medium bg-[#0466C8]/20 text-[#00F5FF] border border-[#0466C8]/30">
                          {project.category}
                        </span>
                      )}
                    </div>
                    <h3 className="font-['Orbitron'] text-xl font-bold text-[#00F5FF] mb-2">
                      {project.title}
                    </h3>
                    {project.description && (
                      <p className="text-[var(--text-secondary)] mb-4 line-clamp-3">
                        {project.description}
                      </p>
                    )}
                    <div className="flex flex-wrap gap-2 mb-4">
                      {(Array.isArray(project.technologies)
                        ? project.technologies
                        : String(project.technologies || "").split(",").filter(Boolean)
                      )
                        .slice(0, 3)
                        .map((tech: string, index: number) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-[#0466C8]/10 text-[#00F5FF] border border-[#0466C8]/20 rounded text-xs"
                          >
                            {tech}
                          </span>
                        ))}
                      {Array.isArray(project.technologies) && project.technologies.length > 3 && (
                        <span className="px-2 py-1 bg-[#0466C8]/10 text-[#00F5FF] border border-[#0466C8]/20 rounded text-xs">
                          +{project.technologies.length - 3} more
                        </span>
                      )}
                    </div>
                    <Button
                      asChild
                      size="sm"
                      className="bg-[#0466C8] hover:bg-[#0466C8]/80 text-white w-full"
                    >
                      <Link to={`/projects/${project.id}`}>View Details</Link>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-6">
              <p className="text-sm text-white/60">No featured projects available yet.</p>
            </div>
          )}

          <div className="text-center">
            <Button
              asChild
              className="bg-[#00F5FF] hover:bg-[#00F5FF]/80 text-[#001233] px-8 py-3"
            >
              <Link to="/projects">View All Projects</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Upcoming Events Section */}
      <section className="upcoming-events-section py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="font-['Orbitron'] text-4xl md:text-5xl font-bold text-[#00F5FF] mb-4">
              Upcoming <span className="text-[var(--text)]">Events</span>
            </h2>
            <p className="text-xl font-['Rajdhani'] text-[var(--text-secondary)] max-w-3xl mx-auto">
              Join us for workshops, seminars, and networking opportunities
            </p>
          </div>

          {upcomingEvents.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {upcomingEvents.map((event) => (
                <div
                  key={event.id}
                  className="project-detail-card overflow-hidden hover:border-[var(--secondary)]/60 transition-all duration-300 group"
                >
                  <div className="h-48 overflow-hidden">
                    <img
                      src={event.image || event.hero_image || event.url || ""}
                      alt={event.title}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                  </div>
                  <div className="p-6">
                    <div className="flex items-center gap-2 mb-3">
                      {event.type && (
                        <span
                          className={`px-3 py-1 rounded-full text-xs font-medium ${
                            event.type === "Workshop"
                              ? "bg-purple-500/20 text-purple-400 border border-purple-500/30"
                              : event.type === "Seminar"
                              ? "bg-orange-500/20 text-orange-400 border border-orange-500/30"
                              : "bg-green-500/20 text-green-400 border border-green-500/30"
                          }`}
                        >
                          {event.type}
                        </span>
                      )}
                    </div>
                    <h3 className="font-['Orbitron'] text-xl font-bold text-[#00F5FF] mb-2">
                      {event.title}
                    </h3>
                    {event.description && (
                      <p className="text-[var(--text-secondary)] mb-4 line-clamp-2">
                        {event.description}
                      </p>
                    )}
                    <div className="space-y-2 mb-4">
                      {event.date && (
                        <div className="flex items-center text-[var(--text-secondary)] text-sm">
                          <Calendar className="mr-2 h-4 w-4 text-[#00F5FF]" />
                          {event.date}
                        </div>
                      )}
                      {event.location && (
                        <div className="flex items-center text-[var(--text-secondary)] text-sm">
                          <MapPin className="mr-2 h-4 w-4 text-[#00F5FF]" />
                          {event.location}
                        </div>
                      )}
                      {typeof event.capacity !== "undefined" && (
                        <div className="flex items-center text-[var(--text-secondary)] text-sm">
                          <Users className="mr-2 h-4 w-4 text-[#00F5FF]" />
                          {event.capacity} attendees
                        </div>
                      )}
                    </div>
                    <Button
                      asChild
                      size="sm"
                      className="bg-[#0466C8] hover:bg-[#0466C8]/80 text-white w-full"
                    >
                      <Link to={`/events/${event.id}`}>View Details</Link>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-6">
              <p className="text-sm text-white/60">No upcoming events available yet.</p>
            </div>
          )}
        </div>
      </section>
    </Layout>
  );
};

export default Index;
