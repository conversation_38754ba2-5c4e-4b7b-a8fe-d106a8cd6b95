import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import LoginModal from "./LoginModal";
import { useAuth } from "../hooks/useAuth";
import { User, LogOut } from "lucide-react";

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();
  const { user, isAuthenticated, login, logout } = useAuth();

  const isActive = (path: string) => location.pathname === path;

  const handleLoginSuccess = (userData: any) => {
    login(userData);
    console.log("User logged in:", userData);
  };

  const handleLogout = () => {
    logout();
    console.log("User logged out");
  };

  return (
    <nav className="navbar">
      <div className="navbar-logo">
        <Link to="/">
          <span className="neon-text">MESL</span>
        </Link>
      </div>

      <div className="navbar-links">
        <Link to="/" className={isActive("/") ? "active" : ""}>
          Home
        </Link>
        <Link to="/projects" className={isActive("/projects") ? "active" : ""}>
          Projects
        </Link>
        <Link to="/events" className={isActive("/events") ? "active" : ""}>
          Events
        </Link>
        <Link to="/feed" className={isActive("/feed") ? "active" : ""}>
          Feed
        </Link>
        <Link to="/gallery" className={isActive("/gallery") ? "active" : ""}>
          Gallery
        </Link>
        <Link to="/team" className={isActive("/team") ? "active" : ""}>
          Team
        </Link>
        <div className="navbar-auth">
          {isAuthenticated ? (
            <div className="flex items-center gap-4">
              {user?.role === 'admin' && (
                <Link
                  to="/admin"
                  className={`flex items-center gap-2 text-sm hover:text-[#00F5FF] transition-colors ${
                    isActive("/admin") ? "text-[#00F5FF]" : ""
                  }`}
                >
                  <span>Admin</span>
                </Link>
              )}
              <Link
                to="/profile"
                className={`flex items-center gap-2 text-sm hover:text-[#00F5FF] transition-colors ${
                  isActive("/profile") ? "text-[#00F5FF]" : ""
                }`}
              >
                <User className="h-4 w-4" />
                <span>{user?.name || user?.email}</span>
              </Link>
              <button
                onClick={handleLogout}
                className="btn-login flex items-center gap-2"
                title="Logout"
              >
                <LogOut className="h-4 w-4" />
                Logout
              </button>
            </div>
          ) : (
            <LoginModal onLoginSuccess={handleLoginSuccess}>
              <button className="btn-signup">Join Us</button>
            </LoginModal>
          )}
        </div>
      </div>

      <div className="navbar-toggle" onClick={() => setIsMenuOpen(!isMenuOpen)}>
        <span></span>
        <span></span>
        <span></span>
      </div>
    </nav>
  );
};

export default Navbar;
