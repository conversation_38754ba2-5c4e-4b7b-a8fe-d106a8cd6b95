from fastapi import APIRouter
from sqlmodel import select
from typing import List
from api.deps import SessionDep, CurrentUser
from models import User
import uuid

router = APIRouter(prefix="/users", tags=["users"])

@router.get("/", response_model=List[dict])
def list_users(session: SessionDep):
    users = session.exec(select(User)).all()
    return [
        {
            "id": str(user.id),
            "username": user.username,
            "full_name": user.full_name,
            "status": user.status.value if hasattr(user.status, 'value') else str(user.status),
            "department": user.department.value if hasattr(user.department, 'value') else str(user.department)
        }
        for user in users
    ]

@router.get("/me")
def get_current_user_info(user: CurrentUser):
    """Get current user information"""
    return {
        "id": str(user.id),
        "username": user.username,
        "full_name": user.full_name,
        "email": user.username,  # Assuming username is email
        "is_admin": user.is_admin,
        "status": user.status.value if hasattr(user.status, 'value') else str(user.status),
        "department": user.department.value if hasattr(user.department, 'value') else str(user.department)
    }

@router.get("/count")
def get_users_count(session: SessionDep):
    """Get total number of registered users"""
    users = session.exec(select(User)).all()
    count = len(users)
    return {"total": count}
