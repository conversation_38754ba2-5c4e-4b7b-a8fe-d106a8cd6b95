import React, { useState } from "react";
import Layout from "@/components/Layout";
import { Card, CardContent } from "@/components/ui/card";
import { IoLogoLinkedin, IoMail, IoLogoGithub } from "react-icons/io5";

const Team = () => {
  const [members, setMembers] = useState<any[]>([]);

  return (
    <Layout>
      <div className="page-content container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="font-['Orbitron'] text-4xl md:text-5xl font-bold text-[#00F5FF] mb-4">
            Our <span className="text-[--text]">Team</span>
          </h1>
          <p className="text-xl font-['Rajdhani'] text-[--text-secondary] max-w-3xl mx-auto">
            Meet the brilliant minds driving innovation at MESL
          </p>
        </div>

        {/* Members Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {members.map((member) => (
            <Card
              key={member.id}
              className="bg-[--card-bg] backdrop-blur-sm border border-[#00F5FF]/20 hover:border-[#00F5FF]/40 transition-all duration-300"
            >
              <CardContent className="p-4 text-center">
                <div className="mb-3">
                  <img
                    src={member.avatar}
                    alt={member.name}
                    className="w-24 h-24 rounded-full mx-auto object-cover border-2 border-[#00F5FF]/30"
                  />
                </div>
                <h3 className="font-['Orbitron'] text-sm font-semibold text-[#00F5FF] mb-1">
                  {member.name}
                </h3>
                {member.position && (
                  <p className="font-['Rajdhani'] text-[--text-secondary] text-xs mb-1">
                    {member.position}
                  </p>
                )}
                {member.specialization && (
                  <p className="font-['Rajdhani'] text-[--text-secondary] text-xs mb-2">
                    {member.specialization}
                  </p>
                )}
                <div className="flex justify-center space-x-4">
                  {member.email && (
                    <a
                      href={`mailto:${member.email}`}
                      className="text-[--text-secondary] hover:text-[#00F5FF] transition-colors"
                    >
                      <IoMail className="w-5 h-5" />
                    </a>
                  )}
                  {member.socials?.github && (
                    <a
                      href={member.socials.github}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-[--text-secondary] hover:text-[#00F5FF] transition-colors"
                    >
                      <IoLogoGithub className="w-5 h-5" />
                    </a>
                  )}
                  {member.socials?.linkedin && (
                    <a
                      href={member.socials.linkedin}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-[--text-secondary] hover:text-[#00F5FF] transition-colors"
                    >
                      <IoLogoLinkedin className="w-5 h-5" />
                    </a>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {members.length === 0 && (
          <div className="text-center py-12">
            <h3 className="font-['Orbitron'] text-2xl text-white/70 mb-4">
              No team members found
            </h3>
            <p className="font-['Rajdhani'] text-white/50">
              Add team members from the Admin Dashboard.
            </p>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default Team;
