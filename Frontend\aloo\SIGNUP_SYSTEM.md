# User Signup System Documentation

## Overview

This document describes the complete user signup system implementation for the MESL frontend application. The system follows an invite-based registration flow where only administrators can invite new users.

## System Architecture

### 1. **Invite-Based Registration Flow**

```
Admin → Send Invite → User Receives Email → User Clicks Link → User Completes Signup → Account Created
```

### 2. **Components Overview**

#### **Frontend Components:**
- `Signup.tsx` - Main signup page for completing registration
- `AdminInvite.tsx` - Modal for admins to send invitations
- `InviteManager.tsx` - Dashboard for managing pending invitations
- `InviteHandler.tsx` - Processes invite links from emails

#### **Backend Endpoints (from README):**
- `POST /auth/invite` - Send invitation email
- `POST /auth/accept-invite` - Complete user registration
- `POST /auth/access-token` - User authentication
- `POST /auth/refresh-token` - Token refresh

## Detailed Flow

### 1. **Admin Sends Invitation**

**Location:** Admin Dashboard → Users Tab → "Invite User" Button

**Process:**
1. <PERSON><PERSON> clicks "Invite User" button
2. <PERSON><PERSON> opens with email input field
3. Admin enters user's email address
4. System sends POST request to `/auth/invite`
5. Backend sends email with invitation link
6. Invitation appears in pending invitations list

**API Call:**
```typescript
POST /auth/invite
Headers: { Authorization: "Bearer <admin_token>" }
Body: { "email": "<EMAIL>" }
```

### 2. **User Receives Invitation Email**

**Email contains link in format:**
```
http://SITEURL/#access_token=<token>&expires_at=<time>&expires_in=<seconds>&refresh_token=<token>&token_type=bearer&type=invite
```

### 3. **User Clicks Invitation Link**

**Process:**
1. User clicks link in email
2. `InviteHandler` component processes the URL hash parameters
3. Invite tokens are stored in sessionStorage
4. User is redirected to `/signup` page with parameters

**InviteHandler Logic:**
```typescript
// Extract parameters from URL hash
const params = new URLSearchParams(hash.substring(1));
const accessToken = params.get("access_token");
const type = params.get("type");

// Validate invite link
if (type !== "invite" || !accessToken) {
  // Show error
}

// Store tokens and redirect
sessionStorage.setItem("invite_token", accessToken);
navigate("/signup?...");
```

### 4. **User Completes Registration**

**Location:** `/signup` page

**Form Fields:**
- Full Name (required)
- Username (required)
- Department (required)
- Password (min 8 characters, required)
- Confirm Password (must match, required)

**Validation:**
- All fields are required
- Password minimum 8 characters
- Passwords must match
- Email format validation (for invite token)

**API Call:**
```typescript
POST /auth/accept-invite
Headers: { 
  Authorization: "Bearer <invite_token>",
  Content-Type: "application/json"
}
Body: {
  "department": "Computer Science",
  "full_name": "John Doe",
  "username": "johndoe",
  "password": "securepassword"
}
```

### 5. **Account Creation Success**

**Process:**
1. Backend creates user account
2. Frontend shows success message
3. User is automatically redirected to feed
4. Invite tokens are cleaned up from sessionStorage

## File Structure

```
src/
├── pages/
│   └── Signup.tsx                 # Main signup page
├── components/
│   ├── AdminInvite.tsx           # Admin invite modal
│   ├── InviteManager.tsx         # Invitation management dashboard
│   └── InviteHandler.tsx         # URL hash parameter processor
└── pages/
    └── AdminDashboard.tsx        # Updated with Users tab
```

## Routes Added

```typescript
// In App.tsx
<Route path="/signup" element={<Signup />} />
```

## Admin Dashboard Integration

**New Tab Added:** "Users"
- Contains `InviteManager` component
- Shows pending invitations
- Provides "Invite User" functionality
- Displays invitation status (Pending, Accepted, Expired)

## Security Features

### 1. **Token-Based Invitations**
- Invite links contain temporary access tokens
- Tokens have expiration times
- Tokens are validated on the backend

### 2. **Admin-Only Invitations**
- Only authenticated admins can send invitations
- Backend validates admin permissions

### 3. **Secure Password Requirements**
- Minimum 8 characters
- Password confirmation required
- Client-side validation

### 4. **Token Cleanup**
- Invite tokens are removed after successful signup
- Prevents token reuse

## Error Handling

### 1. **Invalid Invite Links**
- Missing or malformed parameters
- Expired invitation tokens
- Already used invitations

### 2. **Form Validation Errors**
- Required field validation
- Password strength requirements
- Password mismatch detection

### 3. **API Errors**
- Network connectivity issues
- Backend validation errors
- Authentication failures

## Usage Instructions

### For Administrators:

1. **Login** to the admin account
2. **Navigate** to Admin Dashboard
3. **Click** on "Users" tab
4. **Click** "Invite User" button
5. **Enter** user's email address
6. **Click** "Send Invitation"
7. **Monitor** invitation status in the list

### For New Users:

1. **Check email** for invitation
2. **Click** the invitation link
3. **Fill out** the registration form:
   - Full Name
   - Username
   - Department
   - Password (min 8 chars)
   - Confirm Password
4. **Click** "Complete Registration"
5. **Wait** for success confirmation
6. **Access** the MESL platform

## Testing the System

### 1. **Test Admin Invite Flow**
```bash
# Login as admin
POST /auth/access-token
Body: { username: "<EMAIL>", password: "securepassword" }

# Send invitation
POST /auth/invite
Headers: { Authorization: "Bearer <admin_token>" }
Body: { email: "<EMAIL>" }
```

### 2. **Test Signup Flow**
1. Manually create invite URL with test token
2. Navigate to signup page
3. Fill out form and submit
4. Verify account creation

### 3. **Test Error Cases**
- Invalid invite tokens
- Expired invitations
- Duplicate usernames
- Weak passwords

## Future Enhancements

1. **Email Templates** - Custom invitation email design
2. **Bulk Invitations** - Invite multiple users at once
3. **User Management** - View, edit, and deactivate users
4. **Role Management** - Assign different user roles
5. **Invitation Analytics** - Track invitation success rates
6. **Resend Invitations** - Resend expired or lost invitations

## Troubleshooting

### Common Issues:

1. **"Invalid invite link"** - Check URL parameters and token expiration
2. **"Authentication required"** - Ensure admin is logged in
3. **"Failed to send invitation"** - Check backend connectivity and admin permissions
4. **"Signup failed"** - Verify form validation and backend API availability

### Debug Steps:

1. Check browser console for JavaScript errors
2. Verify network requests in browser dev tools
3. Confirm backend server is running
4. Check sessionStorage for invite tokens
5. Validate admin authentication status
