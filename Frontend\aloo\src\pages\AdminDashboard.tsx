import Layout from "@/components/Layout";
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { useState, useEffect } from "react";
import {
  Plus,
  Trash2,
  Save,
  Pencil,
  Upload,
  AlertTriangle,
} from "lucide-react";
import {
  projectsApi,
  eventsApi,
  postsApi,
  topicsApi,
  type ProjectResponse,
  type EventResponse,
  type PostRead,
  type Topic,
} from "@/lib/api";
import InviteManager from "@/components/InviteManager";

// Real API state management
function useApiData<T>() {
  const [items, setItems] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  return { items, setItems, loading, setLoading, error, setError };
}

const AdminDashboard = () => {
  // Projects
  const projectsState = useApiData<ProjectResponse>();
  const [editingProject, setEditingProject] = useState<any | null>(null);

  // Events
  const eventsState = useApiData<EventResponse>();
  const [editingEvent, setEditingEvent] = useState<any | null>(null);

  // Feed/Posts
  const postsState = useApiData<PostRead>();

  // Topics for dropdowns
  const [topics, setTopics] = useState<Topic[]>([]);

  // Gallery and Team are disabled (no backend APIs)
  const [gallery] = useState<any[]>([]);
  const [team] = useState<any[]>([]);

  // Load data on component mount
  useEffect(() => {
    loadProjects();
    loadEvents();
    loadPosts();
    loadTopics();
  }, []);

  const loadProjects = async () => {
    try {
      projectsState.setLoading(true);
      const data = await projectsApi.list();
      projectsState.setItems(data);
    } catch (error) {
      projectsState.setError(
        error instanceof Error ? error.message : "Failed to load projects"
      );
    } finally {
      projectsState.setLoading(false);
    }
  };

  const loadEvents = async () => {
    try {
      eventsState.setLoading(true);
      const data = await eventsApi.list();
      eventsState.setItems(data);
    } catch (error) {
      eventsState.setError(
        error instanceof Error ? error.message : "Failed to load events"
      );
    } finally {
      eventsState.setLoading(false);
    }
  };

  const loadPosts = async () => {
    try {
      postsState.setLoading(true);
      const data = await postsApi.list();
      postsState.setItems(data);
    } catch (error) {
      postsState.setError(
        error instanceof Error ? error.message : "Failed to load posts"
      );
    } finally {
      postsState.setLoading(false);
    }
  };

  const loadTopics = async () => {
    try {
      const data = await topicsApi.list();
      setTopics(data);
    } catch (error) {
      console.error("Failed to load topics:", error);
    }
  };

  // Project handlers
  const handleCreateProject = async (project: any) => {
    try {
      const projectData = {
        title: project.title,
        overview: project.description,
        status: project.status || "proposed",
        proposed_date: new Date().toISOString(),
        start_date: null,
        end_date: null,
        members: project.technologies
          ? project.technologies.split(",").map((t: string) => t.trim())
          : null,
        topics: null,
      };
      await projectsApi.create(projectData);
      await loadProjects();
      setEditingProject(null);
    } catch (error) {
      console.error("Failed to create project:", error);
    }
  };

  const handleUpdateProject = async (project: any) => {
    try {
      const projectData = {
        title: project.title,
        overview: project.description,
        status: project.status,
      };
      await projectsApi.update(project.id, projectData);
      await loadProjects();
      setEditingProject(null);
    } catch (error) {
      console.error("Failed to update project:", error);
    }
  };

  const handleDeleteProject = async (id: string) => {
    try {
      await projectsApi.remove(id);
      await loadProjects();
    } catch (error) {
      console.error("Failed to delete project:", error);
    }
  };

  // Event handlers
  const handleCreateEvent = async (event: any) => {
    try {
      const eventData = {
        title: event.title,
        date: event.date,
        time: event.time,
        location: event.location,
        description: event.description,
        status: event.status || "upcoming",
        topics: null,
      };
      await eventsApi.create(eventData);
      await loadEvents();
      setEditingEvent(null);
    } catch (error) {
      console.error("Failed to create event:", error);
    }
  };

  const handleUpdateEvent = async (event: any) => {
    try {
      const eventData = {
        title: event.title,
        date: event.date,
        time: event.time,
        location: event.location,
        description: event.description,
        status: event.status,
      };
      await eventsApi.update(event.id, eventData);
      await loadEvents();
      setEditingEvent(null);
    } catch (error) {
      console.error("Failed to update event:", error);
    }
  };

  const handleDeleteEvent = async (id: string) => {
    try {
      await eventsApi.remove(id);
      await loadEvents();
    } catch (error) {
      console.error("Failed to delete event:", error);
    }
  };

  // helper: read file to data URL for localStorage persistence
  const readFileAsDataURL = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(String(reader.result));
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };

  return (
    <Layout>
      <div className="page-content container mx-auto px-4 py-8">
        <Card className="project-detail-card mb-6">
          <CardHeader>
            <h1 className="font-['Orbitron'] text-2xl md:text-3xl font-bold text-[#00F5FF]">
              Admin Dashboard
            </h1>
            <p className="text-[var(--text-secondary)]">
              Admin CMS connected to backend database. Gallery and Team
              management disabled (no backend APIs available).
            </p>
          </CardHeader>
        </Card>

        <Tabs defaultValue="projects">
          <TabsList className="bg-[var(--card-bg)] border border-[var(--border)]">
            <TabsTrigger value="projects">Projects</TabsTrigger>
            <TabsTrigger value="events">Events</TabsTrigger>
            <TabsTrigger value="users">Users</TabsTrigger>
            <TabsTrigger value="gallery" disabled className="opacity-50">
              Gallery (No API)
            </TabsTrigger>
            <TabsTrigger value="team" disabled className="opacity-50">
              Team (No API)
            </TabsTrigger>
            <TabsTrigger value="feed">Feed</TabsTrigger>
          </TabsList>

          {/* Projects */}
          <TabsContent value="projects" className="mt-6">
            <div className="flex justify-end mb-4">
              <Button
                onClick={() =>
                  setEditingProject({
                    title: "",
                    description: "",
                    status: "Active",
                    category: "",
                    hero_image: "",
                    technologies: "",
                  })
                }
                className="bg-[var(--accent)] text-[var(--text)] hover:bg-[var(--secondary)]"
              >
                <Plus className="w-4 h-4 mr-2" /> New Project
              </Button>
            </div>

            {projectsState.loading && <p>Loading projects...</p>}
            {projectsState.error && (
              <p className="text-red-500">Error: {projectsState.error}</p>
            )}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {projectsState.items.map((p) => (
                <Card key={p.id} className="project-detail-card">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-['Orbitron'] text-lg text-[#00F5FF]">
                          {p.title}
                        </h3>
                        <div className="text-xs text-[var(--text-secondary)]">
                          {p.status}
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          size="icon"
                          variant="outline"
                          onClick={() => setEditingProject(p)}
                        >
                          <Pencil className="w-4 h-4" />
                        </Button>
                        <Button
                          size="icon"
                          variant="outline"
                          className="border-red-500 text-red-500 hover:bg-red-500 hover:text-white"
                          onClick={() => handleDeleteProject(p.id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    <p className="text-sm text-[var(--text-secondary)] mt-2 line-clamp-3">
                      {p.overview}
                    </p>
                    <div className="text-xs text-[var(--text-secondary)] mt-2">
                      Status: {p.status} | Proposed:{" "}
                      {new Date(p.proposed_date).toLocaleDateString()}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {editingProject && (
              <Card className="project-detail-card mt-6">
                <CardHeader>
                  <h3 className="font-['Orbitron'] text-xl font-bold text-[#00F5FF]">
                    {editingProject.id ? "Edit Project" : "New Project"}
                  </h3>
                </CardHeader>
                <CardContent className="grid gap-4 md:grid-cols-2">
                  <Input
                    placeholder="Title"
                    value={editingProject.title}
                    onChange={(e) =>
                      setEditingProject({
                        ...editingProject,
                        title: e.target.value,
                      })
                    }
                  />
                  <Input
                    placeholder="Category"
                    value={editingProject.category}
                    onChange={(e) =>
                      setEditingProject({
                        ...editingProject,
                        category: e.target.value,
                      })
                    }
                  />
                  <Input
                    placeholder="Status"
                    value={editingProject.status}
                    onChange={(e) =>
                      setEditingProject({
                        ...editingProject,
                        status: e.target.value,
                      })
                    }
                  />
                  <Input
                    placeholder="Technologies (comma separated)"
                    value={editingProject.technologies}
                    onChange={(e) =>
                      setEditingProject({
                        ...editingProject,
                        technologies: e.target.value,
                      })
                    }
                  />
                  {editingProject.hero_image && (
                    <img
                      src={editingProject.hero_image}
                      alt="preview"
                      className="w-full h-36 object-cover rounded mb-2 md:col-span-2"
                    />
                  )}
                  <label className="md:col-span-2 flex items-center justify-between p-3 rounded border-2 border-dashed border-[var(--accent)] hover:bg-[var(--accent)]/10 cursor-pointer">
                    <div className="text-sm">
                      <div className="font-medium text-[--text]">
                        Hero Image
                      </div>
                      <div className="text-[var(--text-secondary)]">
                        Click to choose an image from your device
                      </div>
                    </div>
                    <div className="inline-flex items-center gap-2 px-3 py-2 rounded bg-[var(--accent)] text-[var(--text)]">
                      <Upload className="w-4 h-4" /> Choose File
                    </div>
                    <input
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={async (e) => {
                        const file = e.target.files?.[0];
                        if (file) {
                          const dataUrl = await readFileAsDataURL(file);
                          setEditingProject({
                            ...editingProject,
                            hero_image: dataUrl,
                          });
                        }
                      }}
                    />
                  </label>
                  <Textarea
                    placeholder="Description"
                    value={editingProject.description}
                    onChange={(e) =>
                      setEditingProject({
                        ...editingProject,
                        description: e.target.value,
                      })
                    }
                    className="md:col-span-2"
                    rows={4}
                  />
                  <div className="flex justify-end md:col-span-2 gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setEditingProject(null)}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={() =>
                        editingProject.id
                          ? handleUpdateProject(editingProject)
                          : handleCreateProject(editingProject)
                      }
                      className="bg-[var(--accent)] text-[var(--text)] hover:bg-[var(--secondary)]"
                    >
                      <Save className="w-4 h-4 mr-2" /> Save
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Events */}
          <TabsContent value="events" className="mt-6">
            <div className="flex justify-end mb-4">
              <Button
                onClick={() =>
                  setEditingEvent({
                    title: "",
                    description: "",
                    type: "Workshop",
                    date: "",
                    time: "",
                    location: "",
                    capacity: 0,
                    hero_image: "",
                    status: "upcoming",
                    tags: "",
                  })
                }
                className="bg-[var(--accent)] text-[var(--text)] hover:bg-[var(--secondary)]"
              >
                <Plus className="w-4 h-4 mr-2" /> New Event
              </Button>
            </div>

            {eventsState.loading && <p>Loading events...</p>}
            {eventsState.error && (
              <p className="text-red-500">Error: {eventsState.error}</p>
            )}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {eventsState.items.map((e) => (
                <Card key={e.id} className="project-detail-card">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-['Orbitron'] text-lg text-[#00F5FF]">
                          {e.title}
                        </h3>
                        <div className="text-xs text-[var(--text-secondary)]">
                          {e.status} • {new Date(e.date).toLocaleDateString()}
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          size="icon"
                          variant="outline"
                          onClick={() => setEditingEvent(e)}
                        >
                          <Pencil className="w-4 h-4" />
                        </Button>
                        <Button
                          size="icon"
                          variant="outline"
                          className="border-red-500 text-red-500 hover:bg-red-500 hover:text-white"
                          onClick={() => handleDeleteEvent(e.id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    <p className="text-sm text-[var(--text-secondary)] mt-2 line-clamp-3">
                      {e.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>

            {editingEvent && (
              <Card className="project-detail-card mt-6">
                <CardHeader>
                  <h3 className="font-['Orbitron'] text-xl font-bold text-[#00F5FF]">
                    {editingEvent.id ? "Edit Event" : "New Event"}
                  </h3>
                </CardHeader>
                <CardContent className="grid gap-4 md:grid-cols-2">
                  <Input
                    placeholder="Title"
                    value={editingEvent.title}
                    onChange={(e) =>
                      setEditingEvent({
                        ...editingEvent,
                        title: e.target.value,
                      })
                    }
                  />
                  <Input
                    placeholder="Type"
                    value={editingEvent.type}
                    onChange={(e) =>
                      setEditingEvent({ ...editingEvent, type: e.target.value })
                    }
                  />
                  <Input
                    placeholder="Status (upcoming/past)"
                    value={editingEvent.status}
                    onChange={(e) =>
                      setEditingEvent({
                        ...editingEvent,
                        status: e.target.value,
                      })
                    }
                  />
                  <Input
                    placeholder="Date"
                    value={editingEvent.date}
                    onChange={(e) =>
                      setEditingEvent({ ...editingEvent, date: e.target.value })
                    }
                  />
                  <Input
                    placeholder="Time"
                    value={editingEvent.time}
                    onChange={(e) =>
                      setEditingEvent({ ...editingEvent, time: e.target.value })
                    }
                  />
                  <Input
                    placeholder="Location"
                    value={editingEvent.location}
                    onChange={(e) =>
                      setEditingEvent({
                        ...editingEvent,
                        location: e.target.value,
                      })
                    }
                  />
                  <Input
                    type="number"
                    placeholder="Capacity"
                    value={editingEvent.capacity}
                    onChange={(e) =>
                      setEditingEvent({
                        ...editingEvent,
                        capacity: Number(e.target.value),
                      })
                    }
                  />
                  {editingEvent.hero_image && (
                    <img
                      src={editingEvent.hero_image}
                      alt="preview"
                      className="w-full h-36 object-cover rounded mb-2 md:col-span-2"
                    />
                  )}
                  <label className="md:col-span-2 flex items-center justify-between p-3 rounded border-2 border-dashed border-[var(--accent)] hover:bg-[var(--accent)]/10 cursor-pointer">
                    <div className="text-sm">
                      <div className="font-medium text-[--text]">
                        Hero Image
                      </div>
                      <div className="text-[var(--text-secondary)]">
                        Click to choose an image from your device
                      </div>
                    </div>
                    <div className="inline-flex items-center gap-2 px-3 py-2 rounded bg-[var(--accent)] text-[var(--text)]">
                      <Upload className="w-4 h-4" /> Choose File
                    </div>
                    <input
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={async (e) => {
                        const file = e.target.files?.[0];
                        if (file) {
                          const dataUrl = await readFileAsDataURL(file);
                          setEditingEvent({
                            ...editingEvent,
                            hero_image: dataUrl,
                          });
                        }
                      }}
                    />
                  </label>
                  <Input
                    placeholder="Tags (comma separated)"
                    value={editingEvent.tags}
                    onChange={(e) =>
                      setEditingEvent({ ...editingEvent, tags: e.target.value })
                    }
                    className="md:col-span-2"
                  />
                  <Textarea
                    placeholder="Description"
                    value={editingEvent.description}
                    onChange={(e) =>
                      setEditingEvent({
                        ...editingEvent,
                        description: e.target.value,
                      })
                    }
                    className="md:col-span-2"
                    rows={4}
                  />
                  <div className="flex justify-end md:col-span-2 gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setEditingEvent(null)}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={() =>
                        editingEvent.id
                          ? handleUpdateEvent(editingEvent)
                          : handleCreateEvent(editingEvent)
                      }
                      className="bg-[var(--accent)] text-[var(--text)] hover:bg-[var(--secondary)]"
                    >
                      <Save className="w-4 h-4 mr-2" /> Save
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Users */}
          <TabsContent value="users" className="mt-6">
            <InviteManager />
          </TabsContent>

          {/* Gallery - Disabled (No Backend API) */}
          <TabsContent value="gallery" className="mt-6">
            <Card className="project-detail-card">
              <CardContent className="p-8 text-center">
                <AlertTriangle className="w-16 h-16 mx-auto mb-4 text-yellow-500" />
                <h3 className="font-['Orbitron'] text-xl font-bold text-[#00F5FF] mb-2">
                  Gallery Management Unavailable
                </h3>
                <p className="text-[var(--text-secondary)]">
                  Gallery management is currently disabled because no backend
                  API is available for this feature.
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Team - Disabled (No Backend API) */}
          <TabsContent value="team" className="mt-6">
            <Card className="project-detail-card">
              <CardContent className="p-8 text-center">
                <AlertTriangle className="w-16 h-16 mx-auto mb-4 text-yellow-500" />
                <h3 className="font-['Orbitron'] text-xl font-bold text-[#00F5FF] mb-2">
                  Team Management Unavailable
                </h3>
                <p className="text-[var(--text-secondary)]">
                  Team management is currently disabled because no backend API
                  is available for this feature.
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Feed moderation */}
          <TabsContent value="feed" className="mt-6">
            <div className="grid grid-cols-1 gap-6">
              <Card className="project-detail-card">
                <CardHeader>
                  <h3 className="font-['Orbitron'] text-xl font-bold text-[#00F5FF]">
                    All Posts
                  </h3>
                </CardHeader>
                <CardContent className="space-y-3">
                  {postsState.loading && <p>Loading posts...</p>}
                  {postsState.error && (
                    <p className="text-red-500">Error: {postsState.error}</p>
                  )}
                  {postsState.items.length === 0 && !postsState.loading && (
                    <div className="text-[var(--text-secondary)]">
                      No posts found.
                    </div>
                  )}
                  {postsState.items.map((post) => (
                    <div
                      key={post.id}
                      className="border border-[var(--border)] rounded-lg p-3"
                    >
                      <div className="font-['Orbitron'] text-[#00F5FF] mb-1">
                        Author ID: {post.author_id}
                        <span className="text-xs text-[var(--text-secondary)] ml-2">
                          • {new Date(post.created_at).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="text-sm text-[var(--text-secondary)] mb-2">
                        {post.content}
                      </div>
                      <div className="text-xs text-[var(--text-secondary)] mb-2">
                        Likes: {post.likes_count} | Comments:{" "}
                        {post.comments?.length || 0}
                      </div>
                      <div className="flex gap-2 justify-end">
                        <Button
                          variant="outline"
                          className="border-red-500 text-red-500 hover:bg-red-500 hover:text-white"
                          onClick={async () => {
                            try {
                              await postsApi.remove(post.id);
                              await loadPosts();
                            } catch (error) {
                              console.error("Failed to delete post:", error);
                            }
                          }}
                        >
                          <Trash2 className="w-4 h-4 mr-2" /> Delete
                        </Button>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
};

export default AdminDashboard;
