from pydantic import BaseModel
import uuid
from models import CommunityType, User, Department
from fastapi import APIRouter
from sqlmodel import select
from api.deps import SessionDep

class TeamMemberRead(BaseModel):
    id: uuid.UUID
    full_name: str
    username: str
    department: Department
    profile_pic: str | None

    class Config:
        from_attributes = True


class TeamGroupedRead(BaseModel):
    result: list[TeamMemberRead] = []


router = APIRouter(prefix="/team", tags=["team"])


@router.get("/teachers", response_model=TeamGroupedRead)
def get_team(session: SessionDep):
    teachers = session.exec(
        select(User).where(User.community_type == CommunityType.teacher)
    ).all()

    return TeamGroupedRead(result=teachers)

@router.get("/students", response_model=TeamGroupedRead)
def get_team(session: SessionDep):
    students = session.exec(
        select(User).where(User.community_type == CommunityType.student)
    ).all()

    return TeamGroupedRead(result=students)
