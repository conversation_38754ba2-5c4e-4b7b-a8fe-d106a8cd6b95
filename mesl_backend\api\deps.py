import logging
from typing import Annotated, Generator

from dotenv import load_dotenv
from fastapi import Depends, HTTPException
from fastapi.security import OAuth2<PERSON><PERSON>wordBearer
from sqlmodel import Session, create_engine, select
from supabase import Client, create_client
from supabase.lib.client_options import SyncClientOptions

from core.config import settings
from models import User

load_dotenv()

logger = logging.getLogger(__name__)

engine = create_engine(str(settings.SQLALCHEMY_DATABASE_URL))


def get_supabase_client() -> Client:
    return create_client(
        settings.SUPABASE_URL,
        settings.SUPABASE_KEY,
        options=SyncClientOptions(
            auto_refresh_token=False,
            persist_session=False,
        ),
    )


SupabaseDep = Annotated[Client, Depends(get_supabase_client)]


def get_db_session() -> Generator[Session, None, None]:
    with Session(engine) as session:
        yield session


SessionDep = Annotated[Session, Depends(get_db_session)]

reusable_oauth2 = OAuth2PasswordBearer(tokenUrl="/auth/access-token")
TokenDep = Annotated[str, Depends(reusable_oauth2)]


def get_current_user(
    session: SessionDep, token: TokenDep, supabase_client: SupabaseDep
):
    try:
        response = supabase_client.auth.get_user(token)
    except Exception as e:
        logger.error("Error during getting user %s", e)
        raise HTTPException(status_code=401, detail="Invalid token")

    if not response or not getattr(response, "user", None):
        logger.error("No user found in response for token")
        raise HTTPException(status_code=401, detail="Invalid token")

    user = session.exec(select(User).where(User.id == response.user.id)).first()

    if not user:
        logger.error("User not found in database")
        raise HTTPException(status_code=404, detail="User not found")
    return user


CurrentUser = Annotated[User, Depends(get_current_user)]

def admin_required(user: CurrentUser):
    if not user.is_admin :
        raise HTTPException(status_code=403, detail="Admin privileges required")
    return user  

Admin = Annotated[User, Depends(admin_required)]