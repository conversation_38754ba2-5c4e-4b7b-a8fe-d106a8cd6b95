import uuid
from datetime import datetime

from fastapi import APIRouter, HTTPException, File, UploadFile, Form
from pydantic import BaseModel, Field
from sqlmodel import select

from api.deps import SessionDep, Admin, SupabaseDep
from models import Project, ProjectMember, ProjectStatus
import logging
import json 

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/project", tags=["project"])


# create
## Create Project
class CreateProjectRequest(BaseModel):
    overview: str
    status: ProjectStatus
    title: str
    proposed_date: datetime = Field(default_factory=datetime.now)
    start_date: datetime | None = None
    end_date: datetime | None = None
    members: list[uuid.UUID] | None = None  # List of user IDs
    
    
class CreateProjectResponse(BaseModel):
    id: uuid.UUID
    overview: str
    status: ProjectStatus
    title: str
    proposed_date: datetime
    banner_url: str | None
    start_date: datetime | None
    end_date: datetime | None
    members: list[ProjectMember] = []

BUCKET_NAME = "project"

@router.post("/")
async def create_project(
    db: SessionDep,
    user: Admin,
    supabase: SupabaseDep,
    banner: UploadFile = File(...),
    overview: str = Form(...),
    status: ProjectStatus = Form(...),
    title: str = Form(...),
    proposed_date: datetime | None = Form(None),
    start_date: datetime | None = Form(None),
    end_date: datetime | None = Form(None),
    members: str | None = Form(None),  # JSON string of UUIDs
):

    # Ensure proposed_date is set
    if not proposed_date:
        proposed_date = datetime.now()

    # Parse members list
    if members:
        try:
            members_list = json.loads(members)
        except Exception:
            members_list = []
    else:
        members_list = []

    # Upload banner to Supabase
    image_url = None
    if banner and banner.filename:
        file_ext = banner.filename.split(".")[-1]
        file_path = f"banners/{uuid.uuid4()}.{file_ext}"

        file_content = await banner.read()

        try:
            supabase.storage.from_(BUCKET_NAME).upload(
                path=file_path, file=file_content
            )
        except Exception as e:
            logger.error(f"Failed to upload file to Supabase: {e}")
            raise HTTPException(status_code=500, detail="Failed to upload file to Supabase storage")

        image_url = supabase.storage.from_(BUCKET_NAME).get_public_url(file_path)
    else:
        raise HTTPException(status_code=400, detail="Banner image is required.")

    # Create Project
    project = Project(
        overview=overview,
        status=status,
        title=title,
        banner_url=image_url,
        proposed_date=proposed_date,
        start_date=start_date,
        end_date=end_date,
    )
    db.add(project)
    db.commit()
    db.refresh(project)

    # Add project members
    for member_id in members_list:
        project_member = ProjectMember(project_id=project.id, user_id=member_id)
        db.add(project_member)
    db.commit()

    db.refresh(project)
    return {"id": project.id}


# read
## Read Project
@router.get("/{project_id}")
async def read_project(project_id: uuid.UUID, db: SessionDep):
    project = db.get_one(Project, project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    return project


## Read All Projects
@router.get("/")
async def read_all_projects(db: SessionDep):
    projects = db.exec(select(Project)).all()
    return projects


## TODO: Implement search functionality


# update
## Update Project Members
@router.put("/{project_id}/members")
async def add_members_to_project(
    project_id: uuid.UUID, db: SessionDep, members: list[uuid.UUID], user: Admin 
):
    project = db.get_one(Project, project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    for member_id in members:
        project_member = ProjectMember(project_id=project.id, user_id=member_id)
        db.add(project_member)

    db.commit()
    return {"message": "Members added successfully"}


## Update Project Details
class UpdateProjectRequest(BaseModel):
    end_date: datetime | None = None
    overview: str | None = None
    proposed_date: datetime | None = None
    start_date: datetime | None = None
    status: ProjectStatus | None = None
    title: str | None = None


@router.patch("/{project_id}")
async def update_project(
    project_id: uuid.UUID, db: SessionDep, request: UpdateProjectRequest, user: Admin
):
    project = db.get_one(Project, project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    for field, value in request.model_dump(exclude_unset=True).items():
        setattr(project, field, value)

    db.commit()
    db.refresh(project)
    return project


# delete
## Remove Project Members
@router.delete("/{project_id}/members")
async def remove_members_from_project(
    project_id: uuid.UUID, db: SessionDep, members: list[uuid.UUID], user: Admin
):
    project = db.get_one(Project, project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    for member_id in members:
        project_member = db.get_one(
            ProjectMember, {"project_id": project.id, "user_id": member_id}
        )
        if project_member:
            db.delete(project_member)

    db.commit()
    return {"message": "Members removed successfully"}


## Delete Project
@router.delete("/{project_id}")
async def delete_project(project_id: uuid.UUID, db: SessionDep, user: Admin):
    project = db.get_one(Project, project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    db.delete(project)
    db.commit()
    return {"message": "Project deleted successfully"}
