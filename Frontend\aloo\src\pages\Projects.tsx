import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { projectsApi } from "@/lib/api";

const Projects = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [allProjects, setAllProjects] = useState<any[]>([]);
  const [filteredProjects, setFilteredProjects] = useState<any[]>(allProjects);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let filtered: any[] = [...allProjects];

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter((project) => {
        const techs = Array.isArray(project.technologies)
          ? project.technologies
          : String(project.technologies || "").split(",").filter(Boolean);
        return (
          String(project.title || "").toLowerCase().includes(term) ||
          String(project.description || "").toLowerCase().includes(term) ||
          techs.some((tech: string) => tech.toLowerCase().includes(term))
        );
      });
    }

    // Filter by category
    if (selectedCategory !== "all") {
      filtered = filtered.filter(
        (project) => String(project.category || "") === selectedCategory
      );
    }

    // Filter by status
    if (selectedStatus !== "all") {
      filtered = filtered.filter(
        (project) => String(project.status || "") === selectedStatus
      );
    }

    setFilteredProjects(filtered);
  }, [searchTerm, selectedCategory, selectedStatus, allProjects]);

  
  useEffect(() => {
    // Load from backend; show error if unavailable
    let active = true;
    projectsApi
      .list()
      .then((list) => {
        if (!active) return;
        const mapped = list.map((p: any) => ({
          id: p.id,
          title: p.title,
          description: p.overview, // map overview to description
          status: p.status,
          image: "",
          hero_image: "",
          technologies: [],
          category: "",
          year: undefined,
          team: undefined,
        }));
        setAllProjects(mapped);
      })
      .catch(() => {
        setError("Unable to load projects right now. Please try again later.");
        setAllProjects([]);
      });
    return () => {
      active = false;
    };
  }, []);

  return (
    <Layout>
      <div className="page-content container mx-auto px-4 py-8">
        {error && (
          <div className="project-detail-card p-4 mb-6 border-red-500/30 bg-red-500/10 text-red-300">
            {error}
          </div>
        )}
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="font-['Orbitron'] text-4xl md:text-5xl font-bold text-[#00F5FF] mb-4">
            Research <span className="text-[--text]">Projects</span>
          </h1>
          <p className="text-xl font-['Rajdhani'] text-[--text-secondary] max-w-3xl mx-auto">
            Explore our cutting-edge research initiatives spanning multiple
            disciplines and technologies
          </p>
        </div>

        {/* Filters */}
        <div className="project-detail-card p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-white/90 mb-2 font-['Orbitron']">
                Search Projects
              </label>
              <Input
                type="text"
                placeholder="Search by title, description, or technology..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="bg-[var(--background)] border-[var(--secondary)]/50 text-[var(--text)] placeholder:text-[var(--text-secondary)]"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-white/90 mb-2 font-['Orbitron']">
                Category
              </label>
              <Select
                value={selectedCategory}
                onValueChange={setSelectedCategory}
              >
                <SelectTrigger className="bg-[var(--background)] border-[var(--secondary)]/50 text-[var(--text)]">
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent className="bg-[var(--card-bg)] border-[var(--secondary)]/50">
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="AI/ML">AI/ML</SelectItem>
                  <SelectItem value="Robotics">Robotics</SelectItem>
                  <SelectItem value="Quantum">Quantum Computing</SelectItem>
                  <SelectItem value="IoT">IoT & Embedded</SelectItem>
                  <SelectItem value="Optics">Optics & AR</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium text-white/90 mb-2 font-['Orbitron']">
                Status
              </label>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="bg-[var(--background)] border-[var(--secondary)]/50 text-[var(--text)]">
                  <SelectValue placeholder="All Status" />
                </SelectTrigger>
                <SelectContent className="bg-[var(--card-bg)] border-[var(--secondary)]/50">
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="Active">Active</SelectItem>
                  <SelectItem value="Completed">Completed</SelectItem>
                  <SelectItem value="Planning">Planning</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end">
              <Button
                onClick={() => {
                  setSearchTerm("");
                  setSelectedCategory("all");
                  setSelectedStatus("all");
                }}
                variant="outline"
                className="w-full border-[#00F5FF] text-[#00F5FF] hover:bg-[#00F5FF] hover:text-[#001233]"
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredProjects.map((project) => (
            <div
              key={project.id}
              className="project-detail-card overflow-hidden hover:border-[var(--secondary)]/60 transition-all duration-300 group"
            >
              <div className="h-48 overflow-hidden">
                <img
                  src={project.image || project.hero_image}
                  alt={project.title}
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                />
              </div>

              <div className="p-6">
                <div className="mb-4">
                  <h3 className="font-['Orbitron'] text-xl font-semibold text-[#00F5FF] mb-2">
                    {project.title}
                  </h3>
                  <div className="flex flex-wrap gap-2 mb-3">
                    {(Array.isArray(project.technologies)
                      ? project.technologies
                      : String(project.technologies || "").split(",").filter(Boolean)
                    ).map((tech: string) => (
                      <span
                        key={tech}
                        className="px-2 py-1 bg-[#0466C8]/20 text-[#00F5FF] text-xs rounded font-['Rajdhani']"
                      >
                        {tech.trim()}
                      </span>
                    ))}
                  </div>
                </div>

                <p className="font-['Rajdhani'] text-white/80 mb-4 line-clamp-3">
                  {project.description}
                </p>

                {(project.team || project.year || project.category) && (
                  <div className="flex justify-between items-center mb-4">
                    <div className="text-sm text-white/60 space-y-1">
                      {project.team && <div>👥 {project.team}</div>}
                      {project.year && <div>📅 {project.year}</div>}
                      {project.category && <div>🏷️ {project.category}</div>}
                    </div>
                  </div>
                )}

                <div className="flex justify-between items-center">
                  <span
                    className={`px-3 py-1 text-xs rounded font-['Rajdhani'] font-semibold ${
                      project.status === "Active"
                        ? "bg-[#00F5FF]/20 text-[#00F5FF]"
                        : project.status === "Completed"
                        ? "bg-green-500/20 text-green-400"
                        : "bg-yellow-500/20 text-yellow-400"
                    }`}
                  >
                    {project.status}
                  </span>
                  <Button
                    asChild
                    size="sm"
                    className="bg-[var(--accent)] text-[var(--text)] hover:bg-[var(--secondary)] hover:text-[var(--text)] transition-colors"
                  >
                    <Link to={`/projects/${project.id}`}>View Details</Link>
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* No Results */}
        {filteredProjects.length === 0 && (
          <div className="text-center py-12">
            <h3 className="font-['Orbitron'] text-2xl text-white/70 mb-4">
              No projects found
            </h3>
            <p className="font-['Rajdhani'] text-white/50">
              Try adjusting your search criteria or clearing the filters.
            </p>
          </div>
        )}

        {/* Empty State when no projects are loaded */}
        {allProjects.length === 0 && (
          <div className="text-center py-6">
            <p className="text-sm text-white/50">
              No projects are available yet.
            </p>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default Projects;
