from contextlib import asynccontextmanager

from fastapi import FastAPI
from sqlmodel import SQLModel
from fastapi.middleware.cors import CORSMiddleware
from api.deps import engine
from api.routers import events, login, project, feed, profile, team


@asynccontextmanager
async def lifespan(app: FastAPI):
    SQLModel.metadata.create_all(engine)
    yield


app = FastAPI(debug=True, lifespan=lifespan)

# List of allowed origins
origins = [
    "http://localhost:8080",  # React dev server
    "http://127.0.0.1:3000",
    "https://your-frontend-domain.com"  # Production frontend domain
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,  # or ["*"] to allow all origins (not secure for prod)
    allow_credentials=True,
    allow_methods=["*"],    # Allow all HTTP methods (GET, POST, PUT, DELETE, etc.)
    allow_headers=["*"],    # Allow all headers
)

app.include_router(login.router)
app.include_router(events.router)
app.include_router(project.router)
app.include_router(feed.router)
app.include_router(profile.router)
app.include_router(team.router)