import { useState, useEffect } from "react";
import { supabase } from "../integrations/supabase/client";

interface User {
  id: string;
  email: string;
  name: string;
  role?: "admin" | "user";
  username?: string;
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

const useAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
  });

  useEffect(() => {
    const loadAuthState = async () => {
      try {
        const storedAuth = sessionStorage.getItem("auth");
        if (storedAuth) {
          const { user, isAuthenticated } = JSON.parse(storedAuth);
          setAuthState({
            user,
            isAuthenticated,
            isLoading: false,
          });
        } else {
          // Check if we have a valid access token from backend authentication
          const accessToken = sessionStorage.getItem("access_token");
          if (accessToken) {
            // We have a token but no stored auth state
            // This could happen if the user refreshed the page
            // For now, we'll just clear the token and require re-login
            sessionStorage.removeItem("access_token");
            sessionStorage.removeItem("refresh_token");
          }

          setAuthState({
            user: null,
            isAuthenticated: false,
            isLoading: false,
          });
        }
      } catch (error) {
        console.error("Error loading auth state:", error);
        sessionStorage.removeItem("auth");
        setAuthState({
          user: null,
          isAuthenticated: false,
          isLoading: false,
        });
      }
    };

    loadAuthState();
  }, []);

  const login = (userData: User) => {
    const newAuthState = {
      user: userData,
      isAuthenticated: true,
      isLoading: false,
    };
    setAuthState(newAuthState);
    sessionStorage.setItem(
      "auth",
      JSON.stringify({
        user: userData,
        isAuthenticated: true,
      })
    );
  };

  const logout = () => {
    // Don't call supabase.auth.signOut() since we're using backend auth
    setAuthState({
      user: null,
      isAuthenticated: false,
      isLoading: false,
    });
    sessionStorage.removeItem("auth");
    sessionStorage.removeItem("access_token");
    sessionStorage.removeItem("refresh_token");
  };

  return {
    ...authState,
    login,
    logout,
  };
};

export { useAuth };
export default useAuth;
