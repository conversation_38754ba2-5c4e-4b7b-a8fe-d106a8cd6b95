import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
// import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useAuth } from "@/hooks/useAuth";
import {
  IoHeart,
  IoHeartOutline,
  IoArrowBack,
  IoSend,
  IoTrash,
  IoCreate,
  IoChatbubbleOutline,
  IoImages,
} from "react-icons/io5";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

import { postsApi, type PostRead, type CommentRead } from "@/lib/api";

// Format: "date MonthName, year" (e.g., "2 October, 2025")
const formatPostDate = (iso: string) => {
  const d = new Date(iso);
  const day = d.getDate();
  const month = d.toLocaleString("en-US", { month: "long" });
  const year = d.getFullYear();
  return `${day} ${month}, ${year}`;
};

interface PostWithUI extends PostRead {
  isLiked?: boolean;
}

interface CommentWithUI extends CommentRead {
  isReplying?: boolean;
  newReply?: string;
  replies?: CommentWithUI[];
}

// Helper functions for user display
const getUserDisplayName = (
  author?: { id: string; username: string; full_name?: string },
  authorId?: string
) => {
  if (author) {
    return author.full_name || author.username || "Unknown User";
  }
  if (authorId) {
    return `User ${authorId.slice(0, 8)}`;
  }
  return "Unknown User";
};

const getUserInitials = (
  author?: { id: string; username: string; full_name?: string },
  authorId?: string
) => {
  if (author) {
    const name = author.full_name || author.username || "??";
    return name.length >= 2 ? name.slice(0, 2).toUpperCase() : "??";
  }
  if (authorId) {
    return authorId.slice(0, 2).toUpperCase();
  }
  return "??";
};

const PostDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  const [post, setPost] = useState<PostWithUI | null>(null);
  const [comments, setComments] = useState<CommentWithUI[]>([]);
  const [newComment, setNewComment] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [userLikes, setUserLikes] = useState<Set<string>>(new Set());

  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState("");
  const [editNewImages, setEditNewImages] = useState<File[]>([]);
  const [editRemoveImageIds, setEditRemoveImageIds] = useState<Set<string>>(
    new Set()
  );
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);

  // Load post and comments
  // Load user's likes from localStorage
  useEffect(() => {
    if (user?.id) {
      const storedLikes = localStorage.getItem(`userLikes_${user.id}`);
      if (storedLikes) {
        try {
          const likedPostIds = JSON.parse(storedLikes);
          setUserLikes(new Set(likedPostIds));
        } catch (err) {
          console.error("Failed to parse stored likes:", err);
        }
      }
    }
  }, [user?.id]);

  useEffect(() => {
    if (!id) {
      setError("No post ID provided");
      return;
    }

    const loadPostDetail = async () => {
      try {
        setLoading(true);
        setError(null);
        console.log("Loading post with ID:", id);

        // Get the post from the posts list
        const posts = await postsApi.list({ limit: 100, offset: 0 });
        console.log("Loaded posts:", posts.length);

        const foundPost = posts.find((p) => p.id === id);
        console.log("Found post:", foundPost ? "Yes" : "No");

        if (!foundPost) {
          setError(`Post with ID ${id} not found`);
          return;
        }

        setPost({ ...foundPost, isLiked: userLikes.has(foundPost.id) });

        // Process comments to add UI state and organize replies
        const processedComments = (foundPost.comments || []).map((comment) => ({
          ...comment,
          isReplying: false,
          newReply: "",
          replies: [], // TODO: Implement nested comments from backend
        }));

        setComments(processedComments);
        console.log("Post loaded successfully");
      } catch (err) {
        console.error("Failed to load post:", err);
        setError(`Failed to load post details: ${err}`);
      } finally {
        setLoading(false);
      }
    };

    loadPostDetail();
  }, [id]);

  const handleLike = async () => {
    if (!post || !isAuthenticated) return;

    const wasLiked = userLikes.has(post.id);
    const newIsLiked = !wasLiked;

    // Optimistic update
    setUserLikes((prev) => {
      const newSet = new Set(prev);
      if (newIsLiked) {
        newSet.add(post.id);
      } else {
        newSet.delete(post.id);
      }
      return newSet;
    });

    setPost({
      ...post,
      likes_count: newIsLiked
        ? (post.likes_count || 0) + 1
        : Math.max((post.likes_count || 0) - 1, 0),
    });

    try {
      await postsApi.like(post.id);

      // Store like status in localStorage for persistence
      const likedPosts = Array.from(userLikes);
      if (newIsLiked) {
        likedPosts.push(post.id);
      } else {
        const index = likedPosts.indexOf(post.id);
        if (index > -1) likedPosts.splice(index, 1);
      }
      localStorage.setItem(`userLikes_${user?.id}`, JSON.stringify(likedPosts));
    } catch (err: any) {
      console.error("Failed to like post:", err);

      // Check if it's an authentication error
      if (
        err.message.includes("401") ||
        err.message.includes("Invalid token")
      ) {
        setError("Your session has expired. Please log in again.");
        // Clear invalid tokens
        sessionStorage.removeItem("access_token");
        sessionStorage.removeItem("refresh_token");
        sessionStorage.removeItem("auth");
      } else {
        setError("Failed to update like status");
      }

      // Revert optimistic update on error
      setUserLikes((prev) => {
        const newSet = new Set(prev);
        if (wasLiked) {
          newSet.add(post.id);
        } else {
          newSet.delete(post.id);
        }
        return newSet;
      });

      setPost({
        ...post,
        likes_count: wasLiked
          ? (post.likes_count || 0) + 1
          : Math.max((post.likes_count || 0) - 1, 0),
      });
    }
  };

  const startEdit = () => {
    if (!post) return;
    setIsEditing(true);
    setEditContent(post.content || "");
    setEditNewImages([]);
    setEditRemoveImageIds(new Set());
  };

  const handleEditImageSelect = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files;
    if (files) {
      const newImages = Array.from(files).filter(
        (file) =>
          file.type.startsWith("image/") && file.size <= 10 * 1024 * 1024
      );
      setEditNewImages((prev) => [...prev, ...newImages].slice(0, 5));
    }
  };

  const removeEditNewImage = (index: number) => {
    setEditNewImages((prev) => prev.filter((_, i) => i !== index));
  };

  const toggleRemoveExistingImage = (imageId: string) => {
    setEditRemoveImageIds((prev) => {
      const next = new Set(prev);
      if (next.has(imageId)) next.delete(imageId);
      else next.add(imageId);
      return next;
    });
  };

  const handleUpdatePost = async () => {
    if (!id || !post) return;
    if (
      !editContent.trim() &&
      editNewImages.length === 0 &&
      editRemoveImageIds.size === 0
    )
      return;
    try {
      setLoading(true);
      const updated = await postsApi.update(id, {
        content: editContent,
        images: editNewImages.length ? editNewImages : undefined,
        remove_image_ids: editRemoveImageIds.size
          ? Array.from(editRemoveImageIds)
          : undefined,
      });
      setPost({
        ...post,
        content: updated.content,
        images: updated.images,
        updated_at: updated.updated_at,
        is_updated: updated.is_updated,
      });
      setIsEditing(false);
      setEditContent("");
      setEditNewImages([]);
      setEditRemoveImageIds(new Set());
    } catch (err) {
      console.error("Failed to update post:", err);
      setError("Failed to update post. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePost = async () => {
    if (!id) return;
    try {
      setLoading(true);
      await postsApi.remove(id);
      navigate("/feed");
    } catch (err) {
      console.error("Failed to delete post:", err);
      setError("Failed to delete post. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleAddComment = async () => {
    if (!newComment.trim() || !post || !isAuthenticated) return;

    try {
      const comment = await postsApi.comment(post.id, { content: newComment });
      setComments([
        ...comments,
        {
          ...comment,
          isReplying: false,
          newReply: "",
          replies: [],
        },
      ]);
      setNewComment("");
    } catch (err: any) {
      console.error("Failed to add comment:", err);
      setError("Failed to add comment");
    }
  };

  const toggleReply = (commentId: string) => {
    setComments(
      comments.map((comment) =>
        comment.id === commentId
          ? { ...comment, isReplying: !comment.isReplying }
          : comment
      )
    );
  };

  const updateReply = (commentId: string, content: string) => {
    setComments(
      comments.map((comment) =>
        comment.id === commentId ? { ...comment, newReply: content } : comment
      )
    );
  };

  const handleAddReply = async (commentId: string) => {
    const comment = comments.find((c) => c.id === commentId);
    if (!comment?.newReply?.trim() || !isAuthenticated) return;

    try {
      // For now, we'll add replies as regular comments
      // In a real app, you'd have a dedicated reply endpoint
      const reply = await postsApi.comment(post!.id, {
        content: `@${getUserDisplayName(null, comment.author_id)}: ${
          comment.newReply
        }`,
      });

      setComments([
        ...comments,
        {
          ...reply,
          isReplying: false,
          newReply: "",
          replies: [],
        },
      ]);

      // Clear reply state
      setComments(
        comments.map((c) =>
          c.id === commentId ? { ...c, isReplying: false, newReply: "" } : c
        )
      );
    } catch (err: any) {
      console.error("Failed to add reply:", err);
      setError("Failed to add reply");
    }
  };

  if (loading) {
    return (
      <div className="page-content container mx-auto px-4 py-8">
        <div className="text-center py-8">
          <div className="text-[#00F5FF]">Loading post...</div>
        </div>
      </div>
    );
  }

  if (error || !post) {
    return (
      <div className="page-content container mx-auto px-4 py-8">
        <div className="text-center py-8">
          <div className="text-red-400 mb-4">{error || "Post not found"}</div>
          <Button
            onClick={() => navigate("/feed")}
            className="bg-[var(--accent)]"
          >
            <IoArrowBack className="w-4 h-4 mr-2" />
            Back to Feed
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="page-content container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* Back Button */}
        <Button
          onClick={() => navigate("/feed")}
          variant="outline"
          className="mb-6 border-[var(--secondary)]/50 text-[var(--text)] hover:bg-[var(--secondary)]/20"
        >
          <IoArrowBack className="w-4 h-4 mr-2" />
          Back to Feed
        </Button>

        {/* Post Detail */}
        <Card className="project-detail-card mb-6">
          <CardContent className="p-6">
            <div className="flex items-start space-x-4">
              <Avatar className="w-12 h-12 border-2 border-[#00F5FF]/30">
                <AvatarFallback className="bg-[#0466C8] text-white">
                  {getUserInitials(post.author)}
                </AvatarFallback>
              </Avatar>

              <div className="flex-1">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex flex-col leading-tight">
                    <h3 className="font-['Orbitron'] font-semibold text-[#00F5FF] leading-tight">
                      {getUserDisplayName(post.author)}
                    </h3>
                    <div className="text-white/50 text-sm font-['Rajdhani'] mt-0.5">
                      {formatPostDate(post.created_at)}
                      {post.is_updated && (
                        <span className="text-yellow-400 text-xs ml-2">
                          (edited)
                        </span>
                      )}
                    </div>
                  </div>

                  {isAuthenticated && user?.id === post.author?.id && (
                    <div className="flex items-center space-x-1">
                      {isEditing ? (
                        <>
                          <Button
                            variant="ghost"
                            onClick={() => {
                              setIsEditing(false);
                              setEditContent("");
                              setEditNewImages([]);
                              setEditRemoveImageIds(new Set());
                            }}
                            className="text-white/70 hover:text-white"
                          >
                            Cancel
                          </Button>
                          <Button
                            onClick={handleUpdatePost}
                            disabled={
                              loading ||
                              (!editContent.trim() &&
                                editNewImages.length === 0 &&
                                editRemoveImageIds.size === 0)
                            }
                            className="bg-[#00F5FF] text-black hover:opacity-90"
                          >
                            Save
                          </Button>
                        </>
                      ) : (
                        <>
                          <Button
                            variant="ghost"
                            onClick={startEdit}
                            className="text-white/70 hover:text-white"
                            title="Edit post"
                          >
                            <IoCreate className="w-5 h-5" />
                          </Button>
                          <Button
                            variant="ghost"
                            onClick={() => setConfirmDeleteOpen(true)}
                            className="text-red-500 hover:text-red-600"
                            title="Delete post"
                          >
                            <IoTrash className="w-5 h-5" />
                          </Button>
                        </>
                      )}
                    </div>
                  )}
                </div>

                {isEditing ? (
                  <div className="space-y-3 mb-4">
                    <Textarea
                      value={editContent}
                      onChange={(e) => setEditContent(e.target.value)}
                      className="w-full min-h-[80px]"
                    />

                    {post.images && post.images.length > 0 && (
                      <div className="grid grid-cols-2 gap-3">
                        {post.images.map((image) => (
                          <div
                            key={image.id}
                            className={`relative rounded-lg overflow-hidden border ${
                              editRemoveImageIds.has(image.id)
                                ? "border-red-500/60"
                                : "border-[var(--secondary)]/30"
                            }`}
                          >
                            <img
                              src={image.file_url}
                              alt=""
                              className="w-full h-40 object-cover"
                            />
                            <button
                              onClick={() =>
                                toggleRemoveExistingImage(image.id)
                              }
                              className="absolute top-2 right-2 bg-black/60 text-white text-xs px-2 py-1 rounded"
                            >
                              {editRemoveImageIds.has(image.id)
                                ? "Undo"
                                : "Remove"}
                            </button>
                          </div>
                        ))}
                      </div>
                    )}

                    {editNewImages.length > 0 && (
                      <div className="grid grid-cols-2 gap-3">
                        {editNewImages.map((file, index) => (
                          <div
                            key={index}
                            className="relative rounded-lg overflow-hidden border border-[var(--secondary)]/30"
                          >
                            <img
                              src={URL.createObjectURL(file)}
                              alt=""
                              className="w-full h-40 object-cover"
                            />
                            <button
                              onClick={() => removeEditNewImage(index)}
                              className="absolute top-2 right-2 bg-black/60 text-white text-xs px-2 py-1 rounded"
                            >
                              Remove
                            </button>
                          </div>
                        ))}
                      </div>
                    )}

                    <div>
                      <input
                        type="file"
                        id="edit-image-upload"
                        multiple
                        accept="image/*"
                        onChange={handleEditImageSelect}
                        className="hidden"
                      />
                      <label
                        htmlFor="edit-image-upload"
                        className="inline-flex items-center px-3 py-1.5 rounded-md border border-[var(--secondary)]/30 text-white/80 hover:text-white cursor-pointer"
                      >
                        <IoImages className="w-4 h-4 mr-2 text-[#00F5FF]" />
                        Add Photos ({editNewImages.length}/5)
                      </label>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="mb-4">
                      <p className="font-['Rajdhani'] text-white/80 leading-relaxed text-lg">
                        {post.content}
                      </p>
                    </div>

                    {/* Post Images */}
                    {post.images && post.images.length > 0 && (
                      <div
                        className={`mb-4 ${
                          post.images.length === 1
                            ? "grid grid-cols-1"
                            : post.images.length === 2
                            ? "grid grid-cols-2 gap-3"
                            : post.images.length === 3
                            ? "grid grid-cols-2 gap-3"
                            : "grid grid-cols-2 gap-3"
                        }`}
                      >
                        {post.images.map((image, index) => (
                          <div
                            key={image.id}
                            className={`relative ${
                              post.images!.length === 3 && index === 0
                                ? "col-span-2"
                                : ""
                            }`}
                          >
                            <img
                              src={image.file_url}
                              alt={`Post image ${index + 1}`}
                              className="w-full h-96 object-cover rounded-lg"
                            />
                          </div>
                        ))}
                      </div>
                    )}
                  </>
                )}

                <div className="flex items-center space-x-6 text-white/60">
                  <button
                    onClick={handleLike}
                    disabled={!isAuthenticated}
                    className={`flex items-center space-x-2 transition-all duration-200 disabled:opacity-50 ${
                      userLikes.has(post.id)
                        ? "text-red-400 drop-shadow-[0_0_8px_rgba(239,68,68,0.6)] hover:drop-shadow-[0_0_12px_rgba(239,68,68,0.8)]"
                        : "text-white/60 hover:text-red-400"
                    }`}
                  >
                    {userLikes.has(post.id) ? (
                      <IoHeart className="w-5 h-5" />
                    ) : (
                      <IoHeartOutline className="w-5 h-5" />
                    )}
                    <span className="font-['Rajdhani'] font-semibold">
                      {post.likes_count || 0}
                    </span>
                  </button>

                  <div className="flex items-center space-x-2">
                    <IoChatbubbleOutline className="w-5 h-5" />
                    <span className="font-['Rajdhani']">
                      {comments.length}{" "}
                      {comments.length === 1 ? "comment" : "comments"}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Add Comment - Facebook Style */}
        {isAuthenticated && (
          <Card className="project-detail-card mb-6">
            <CardContent className="p-4">
              <div className="flex space-x-3">
                <Avatar className="w-8 h-8 border border-[#00F5FF]/30 flex-shrink-0">
                  <AvatarFallback className="bg-[#0466C8] text-white text-sm">
                    {user?.id ? getUserInitials(null, user.id) : "??"}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 flex items-end gap-2">
                  <textarea
                    placeholder="Write a comment..."
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    className="flex-1 bg-white text-gray-900 placeholder-gray-500 text-sm px-3 py-2 resize-none overflow-hidden border border-gray-300 rounded-2xl outline-none min-h-[36px]"
                    rows={1}
                    style={{
                      height: "auto",
                      minHeight: "36px",
                    }}
                    onInput={(e) => {
                      const target = e.target as HTMLTextAreaElement;
                      target.style.height = "auto";
                      target.style.height = target.scrollHeight + "px";
                    }}
                    onKeyPress={(e) => {
                      if (
                        e.key === "Enter" &&
                        !e.shiftKey &&
                        newComment.trim()
                      ) {
                        e.preventDefault();
                        handleAddComment();
                      }
                    }}
                  />
                  {newComment.trim() && (
                    <Button
                      onClick={handleAddComment}
                      className="bg-[#1877f2] hover:bg-[#166fe5] text-white rounded-full px-4 py-1 text-xs font-semibold h-7 mb-0.5"
                    >
                      Comment
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Comments - Facebook/Instagram Style */}
        <div className="space-y-2">
          {comments.map((comment) => {
            const isReply = comment.content.startsWith("@");
            return (
              <div
                key={comment.id}
                className={`${
                  isReply ? "ml-8 border-l-2 border-[#00F5FF]/20 pl-4" : ""
                }`}
              >
                <div className="flex space-x-3">
                  <Avatar
                    className={`${
                      isReply ? "w-6 h-6" : "w-8 h-8"
                    } border border-[#00F5FF]/30`}
                  >
                    <AvatarFallback
                      className={`bg-[#0466C8] text-white ${
                        isReply ? "text-xs" : "text-sm"
                      }`}
                    >
                      {getUserInitials(null, comment.author_id)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="bg-[var(--background)]/30 rounded-2xl px-3 py-2 inline-block max-w-full">
                      <div className="flex items-center space-x-2 mb-1">
                        <span
                          className={`font-['Orbitron'] font-semibold text-[#00F5FF] ${
                            isReply ? "text-xs" : "text-sm"
                          }`}
                        >
                          {getUserDisplayName(null, comment.author_id)}
                        </span>
                      </div>
                      <p
                        className={`font-['Rajdhani'] text-white/90 ${
                          isReply ? "text-xs" : "text-sm"
                        } break-words`}
                      >
                        {comment.content}
                      </p>
                    </div>

                    {/* Action buttons under comment bubble */}
                    <div className="flex items-center space-x-4 mt-1 ml-3">
                      <span className="text-white/40 text-xs font-['Rajdhani']">
                        {new Date(comment.created_at).toLocaleString()}
                      </span>
                      {isAuthenticated && !isReply && (
                        <button
                          onClick={() => toggleReply(comment.id)}
                          className="text-xs text-white/60 hover:text-[#00F5FF] transition-colors font-['Rajdhani'] font-semibold"
                        >
                          Reply
                        </button>
                      )}
                    </div>

                    {/* Reply Input - Facebook Style */}
                    {comment.isReplying && (
                      <div className="mt-3 ml-0">
                        <div className="flex space-x-2 items-start">
                          <Avatar className="w-8 h-8 border border-[#00F5FF]/30 flex-shrink-0">
                            <AvatarFallback className="bg-[#0466C8] text-white text-sm">
                              {user?.id ? getUserInitials(null, user.id) : "??"}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1 flex items-end gap-2">
                            <textarea
                              placeholder={`Write a reply to ${getUserDisplayName(
                                null,
                                comment.author_id
                              )}...`}
                              value={comment.newReply || ""}
                              onChange={(e) =>
                                updateReply(comment.id, e.target.value)
                              }
                              className="flex-1 bg-white text-gray-900 placeholder-gray-500 text-sm px-3 py-2 resize-none overflow-hidden border border-gray-300 rounded-2xl outline-none min-h-[36px]"
                              rows={1}
                              style={{
                                height: "auto",
                                minHeight: "36px",
                              }}
                              onInput={(e) => {
                                const target = e.target as HTMLTextAreaElement;
                                target.style.height = "auto";
                                target.style.height =
                                  target.scrollHeight + "px";
                              }}
                              onKeyPress={(e) => {
                                if (
                                  e.key === "Enter" &&
                                  !e.shiftKey &&
                                  comment.newReply?.trim()
                                ) {
                                  e.preventDefault();
                                  handleAddReply(comment.id);
                                }
                              }}
                            />
                            {comment.newReply?.trim() && (
                              <Button
                                size="sm"
                                onClick={() => handleAddReply(comment.id)}
                                className="bg-[#1877f2] hover:bg-[#166fe5] text-white rounded-full px-4 py-1 text-xs font-semibold h-7 mb-0.5"
                              >
                                Reply
                              </Button>
                            )}
                          </div>
                          <div className="flex justify-end mt-1 px-1">
                            <button
                              onClick={() => toggleReply(comment.id)}
                              className="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                            >
                              Cancel
                            </button>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        <AlertDialog
          open={confirmDeleteOpen}
          onOpenChange={(open) => !open && setConfirmDeleteOpen(false)}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete post?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. Are you sure you want to delete
                this post?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setConfirmDeleteOpen(false)}>
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={() => {
                  setConfirmDeleteOpen(false);
                  handleDeletePost();
                }}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
};

export default PostDetail;
