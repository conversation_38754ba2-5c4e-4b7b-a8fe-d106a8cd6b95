import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { IoPersonAdd, IoMail, IoCheckmarkCircle } from "react-icons/io5";

interface InviteFormData {
  email: string;
}

interface AdminInviteProps {
  onInviteSent?: (email: string) => void;
}

const AdminInvite: React.FC<AdminInviteProps> = ({ onInviteSent }) => {
  const [formData, setFormData] = useState<InviteFormData>({ email: "" });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setError(null);
    setSuccess(null);
  };

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.email.trim()) {
      setError("Email address is required");
      return;
    }

    if (!validateEmail(formData.email)) {
      setError("Please enter a valid email address");
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      const token = sessionStorage.getItem("access_token");
      if (!token) {
        throw new Error("You must be logged in to send invitations");
      }

      const response = await fetch(
        `http://localhost:8000/auth/invite?email=${encodeURIComponent(
          formData.email
        )}`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error("Invite API error:", response.status, errorData);

        if (response.status === 422) {
          throw new Error("Invalid email format or missing required fields");
        } else if (response.status === 403) {
          throw new Error("You don't have permission to send invitations");
        } else if (response.status === 500) {
          throw new Error(
            "Server error while sending invitation. Please try again later."
          );
        } else {
          throw new Error(
            errorData.detail || `Failed to send invitation (${response.status})`
          );
        }
      }

      const result = await response.json();
      console.log("Invitation sent:", result);

      setSuccess(`Invitation sent successfully to ${formData.email}`);
      setFormData({ email: "" });

      // Call callback if provided
      if (onInviteSent) {
        onInviteSent(formData.email);
      }

      // Auto-close dialog after success
      setTimeout(() => {
        setIsOpen(false);
        setSuccess(null);
      }, 2000);
    } catch (err: any) {
      console.error("Invite error:", err);
      setError(err.message || "Failed to send invitation");
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({ email: "" });
    setError(null);
    setSuccess(null);
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        setIsOpen(open);
        if (!open) resetForm();
      }}
    >
      <DialogTrigger asChild>
        <Button className="bg-[var(--accent)] text-[var(--text)] hover:bg-[var(--accent)]/80">
          <IoPersonAdd className="w-4 h-4 mr-2" />
          Invite User
        </Button>
      </DialogTrigger>

      <DialogContent className="sm:max-w-md bg-[var(--card)] border-[var(--secondary)]/50">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2 text-[#00F5FF] font-['Orbitron']">
            <IoMail className="w-5 h-5" />
            <span>Invite New User</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {error && (
            <Alert className="border-red-500/30 bg-red-500/10">
              <AlertDescription className="text-red-300">
                {error}
              </AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert className="border-green-500/30 bg-green-500/10">
              <AlertDescription className="text-green-300 flex items-center space-x-2">
                <IoCheckmarkCircle className="w-4 h-4" />
                <span>{success}</span>
              </AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label
                htmlFor="email"
                className="text-white/80 font-['Rajdhani']"
              >
                Email Address
              </Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="<EMAIL>"
                className="bg-[var(--background)] border-[var(--secondary)]/50 text-[var(--text)]"
                required
              />
              <p className="text-xs text-white/50 mt-1">
                An invitation link will be sent to this email address
              </p>
            </div>

            <div className="flex space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsOpen(false)}
                className="flex-1 border-[var(--secondary)]/50 text-[var(--text)] hover:bg-[var(--secondary)]/20"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={loading}
                className="flex-1 bg-[var(--accent)] text-[var(--text)] hover:bg-[var(--accent)]/80"
              >
                {loading ? "Sending..." : "Send Invitation"}
              </Button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AdminInvite;
