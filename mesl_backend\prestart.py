import logging
import uuid

from gotrue.errors import AuthApiError
from sqlmodel import Session

from api.deps import SessionDep, SupabaseDep, engine, get_supabase_client
from core.config import settings
from models import Department, User, UserStatus

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def init_db(db: SessionDep, supabase: SupabaseDep):
    # creates initial admin user if not exists
    try:
        res = supabase.auth.admin.create_user(
            {
                "email": settings.FIRST_ADMIN_EMAIL,
                "password": settings.FIRST_ADMIN_PASSWORD,
                "email_confirm": True,
            }
        )
        user = User(
            id=uuid.UUID(res.user.id),
            department=Department.cse,
            full_name="Admin User",
            is_admin=True,
            username="admin",
            status=UserStatus.active,
        )

        db.add(user)
        db.commit()
        db.refresh(user)
        logger.info("Created default admin user in DB: %s", user)

    except AuthApiError as e:
        if e.to_dict().get("code") == "email_exists":
            logger.info("User already exists, skipping creation.")
        else:
            logger.error("Error creating user: %s", e)
            raise e


with Session(engine) as session:
    init_db(session, get_supabase_client())
