import uuid
from datetime import datetime
from enum import Enum
from typing import Annotated, Optional

from fastapi import Form
from sqlmodel import Field, Relationship, SQLModel
from sqlalchemy import Column,JSON


class Token(SQLModel):
    """
    Represents an authentication token with associated metadata.

    Attributes:
        access_token (str): The access token string used for authentication.
        token_type (str): The type of token, defaults to "bearer".
        expires_in (int): The number of seconds until the token expires.
        expires_at (int | None): The absolute expiration time as a timestamp, or None if not set.
        refresh_token (str): The refresh token used to obtain new access tokens.
    """

    access_token: str
    token_type: str = "bearer"
    expires_in: int
    expires_at: Optional[int]
    refresh_token: str


class Comment(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    author_id: Optional[uuid.UUID] = Field(default=None, foreign_key="user.id")
    author: "User" = Relationship(back_populates="comments")
    content: str
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    is_updated: bool = Field(default=False)
    parent_comment_id: Optional[uuid.UUID] = Field(
        default=None, foreign_key="comment.id"
    )
    parent_comment: Optional["Comment"] = Relationship(
        back_populates="child_comments",
        sa_relationship_kwargs={"remote_side": "Comment.id"},
    )
    post_id: uuid.UUID = Field(foreign_key="post.id")
    post: "Post" = Relationship(back_populates="comments")

    child_comments: list["Comment"] = Relationship(back_populates="parent_comment")
    

class CommunityType(str, Enum):
    teacher = "teacher"
    student = "student"


class Department(str, Enum):
    cse = "cse"
    eee = "eee"
    ce = "ce"


class EventStatus(str, Enum):
    upcoming = "upcoming"
    ongoing = "ongoing"
    archived = "archived"


class Event(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True, gt=0)
    title: str = Field(index=True)
    datetime: str 
    location: str
    description: str
    banner_url: str | None
    status: EventStatus = Field(default=EventStatus.upcoming)
    images: list["EventImage"] = Relationship(
        back_populates="event", cascade_delete=True
        )

class EventImage(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    event_id: uuid.UUID = Field(foreign_key="event.id")
    file_url: str  # Supabase Storage URL
    uploaded_at: datetime = Field(default_factory=datetime.now)
    media_type: str | None = None
    event: Event = Relationship(back_populates="images")


class Post(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    author_id: uuid.UUID = Field(foreign_key="user.id")
    author: "User" = Relationship(back_populates="posts")
    content: str
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    likes_count: int = Field(default=0)
    is_updated: bool = Field(default=False)

    comments: list[Comment] = Relationship(
        back_populates="post", cascade_delete=True
        )
    likes: list["PostLike"] = Relationship(
        back_populates="post", cascade_delete=True
        )
    images: list["PostImage"] = Relationship(
        back_populates="post", cascade_delete=True
        )

class PostLike(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    user_id: uuid.UUID = Field(foreign_key="user.id")
    user: "User" = Relationship(back_populates="liked_posts")
    post_id: uuid.UUID = Field(foreign_key="post.id")
    post: Post = Relationship(back_populates="likes")


class PostImage(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    post_id: uuid.UUID = Field(foreign_key="post.id")
    file_url: str  # Supabase Storage URL
    uploaded_at: datetime = Field(default_factory=datetime.now)
    # media_type: Optional[str] = None (for Version 2.0)
    post: "Post" = Relationship(back_populates="images")


class ProjectStatus(str, Enum):
    """
    Represents the status of a project.

    Values:
        proposed: The project is proposed and not yet started.
        ongoing: The project is currently in progress.
        completed: The project has been finished.
    """

    proposed = "proposed"
    ongoing = "ongoing"
    completed = "completed"
    featured = "featured"


class Project(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    end_date: datetime | None
    overview: str
    proposed_date: datetime
    start_date: datetime | None
    banner_url: str | None
    status: ProjectStatus = Field(default=ProjectStatus.proposed)
    title: str = Field(index=True)

    members: list["ProjectMember"] = Relationship(
        back_populates="project", cascade_delete=True
    )
    # likes: list["ProjectLike"] = Relationship(
    #     back_populates="project", cascade_delete=True
    # )
    images: list["ProjectImage"] = Relationship(
        back_populates="project", cascade_delete=True
    )


class ProjectImage(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    project_id: uuid.UUID = Field(foreign_key="project.id")
    file_url: str  # Supabase Storage URL
    uploaded_at: datetime = Field(default_factory=datetime.now)
    media_type: str | None = None
    project: Project = Relationship(back_populates="images")


# class ProjectLike(SQLModel, table=True):
#     id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
#     user_id: uuid.UUID = Field(foreign_key="user.id")
#     user: "User" = Relationship(back_populates="liked_projects")
#     project_id: uuid.UUID = Field(foreign_key="project.id")
#     project: Project = Relationship(back_populates="likes")


class ProjectMember(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    project_id: uuid.UUID = Field(foreign_key="project.id")
    project: Project = Relationship(back_populates="members")
    user_id: uuid.UUID = Field(foreign_key="user.id")
    user: "User" = Relationship(back_populates="project_members")


class UserStatus(str, Enum):
    active = "active"
    pending = "pending"


class User(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    community_type: CommunityType = Field(default=CommunityType.student)
    department: Department = Field()
    full_name: str = Field()
    is_admin: bool = Field(default=False)
    username: str = Field(index=True)
    status: UserStatus

    profile_pic: str | None = None

    # additional info
    github_link: str | None = None
    linkedin_link: str | None = None
    gmail: str | None = None
    contact: str | None = None
    phone: str | None = None
    bio: str | None = None
    skills: list[str] | None = Field(default=None, sa_column=Column(JSON))  

    comments: list["Comment"] = Relationship(back_populates="author")
    # liked_comments: list[CommentLike] = Relationship(back_populates="user") (for Version 2.0)
    liked_posts: list[PostLike] = Relationship(back_populates="user")
    #liked_projects: list[ProjectLike] = Relationship(back_populates="user")
    posts: list[Post] = Relationship(back_populates="author")
    project_members: list[ProjectMember] = Relationship(back_populates="user")


class UserSignup(SQLModel):
    department: Annotated[Department, Form()]
    full_name: Annotated[str, Form()]
    username: Annotated[str, Form()]
    password: Annotated[str, Form(json_schema_extra={"format": "password"})]
