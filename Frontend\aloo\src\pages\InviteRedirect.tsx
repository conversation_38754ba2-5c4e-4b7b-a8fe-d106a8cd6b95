import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import Layout from "@/components/Layout";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { IoArrowForward, IoWarning } from "react-icons/io5";

/**
 * This page helps users who clicked an invite link that went to the wrong port
 * It provides instructions and a button to manually process the invite
 */
const InviteRedirect = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // Check if there's an invite link in the current URL hash
    const hash = window.location.hash;
    if (hash && hash.includes("type=invite")) {
      // Automatically process the invite if found
      processInviteFromHash(hash);
    }
  }, []);

  const processInviteFromHash = (hash: string) => {
    try {
      const params = new URLSearchParams(hash.substring(1));
      const accessToken = params.get("access_token");
      const expiresAt = params.get("expires_at");
      const expiresIn = params.get("expires_in");
      const refreshToken = params.get("refresh_token");
      const tokenType = params.get("token_type");
      const type = params.get("type");

      if (type === "invite" && accessToken) {
        // Store tokens
        sessionStorage.setItem("invite_token", accessToken);
        if (refreshToken) sessionStorage.setItem("invite_refresh_token", refreshToken);
        if (expiresAt) sessionStorage.setItem("invite_expires_at", expiresAt);

        // Redirect to signup
        const signupUrl = `/signup?access_token=${accessToken}&expires_at=${expiresAt}&expires_in=${expiresIn}&refresh_token=${refreshToken}&token_type=${tokenType}&type=${type}`;
        navigate(signupUrl, { replace: true });
      }
    } catch (error) {
      console.error("Error processing invite:", error);
    }
  };

  const handleManualRedirect = () => {
    // Get the current URL and extract the hash
    const currentUrl = window.location.href;
    const hashIndex = currentUrl.indexOf('#');
    
    if (hashIndex !== -1) {
      const hash = currentUrl.substring(hashIndex);
      processInviteFromHash(hash);
    } else {
      // No hash found, redirect to home
      navigate('/');
    }
  };

  return (
    <Layout>
      <div className="page-content container mx-auto px-4 py-8">
        <div className="max-w-md mx-auto">
          <Card className="project-detail-card">
            <CardContent className="p-6 text-center">
              <IoWarning className="w-16 h-16 text-yellow-400 mx-auto mb-4" />
              
              <h2 className="text-2xl font-['Orbitron'] text-[#00F5FF] mb-4">
                Invitation Link Detected
              </h2>
              
              <p className="text-white/80 font-['Rajdhani'] mb-6 leading-relaxed">
                It looks like you clicked an invitation link, but it may have opened on the wrong port. 
                Click the button below to process your invitation and complete your registration.
              </p>

              <div className="space-y-4">
                <Button 
                  onClick={handleManualRedirect}
                  className="w-full bg-[var(--accent)] text-[var(--text)] hover:bg-[var(--accent)]/80"
                >
                  <IoArrowForward className="w-4 h-4 mr-2" />
                  Process My Invitation
                </Button>

                <Button 
                  onClick={() => navigate('/')}
                  variant="outline"
                  className="w-full border-[var(--secondary)]/50 text-[var(--text)] hover:bg-[var(--secondary)]/20"
                >
                  Go to Home Page
                </Button>
              </div>

              <div className="mt-6 p-4 bg-[var(--background)]/50 rounded-lg">
                <h3 className="font-['Orbitron'] text-sm font-semibold text-[#00F5FF] mb-2">
                  Having trouble?
                </h3>
                <p className="text-xs text-white/60 font-['Rajdhani']">
                  If you're having issues with your invitation, please contact an administrator 
                  or try copying the full invitation URL and pasting it into your browser's address bar, 
                  then changing the port from 3000 to 8081.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
};

export default InviteRedirect;
