import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  Calendar,
  Users,
  ExternalLink,
  Github,
  Download,
  FileText,
  PlusCircle,
  X,
} from "lucide-react";
import jsPDF from "jspdf";
import { projectsApi, topicsApi, usersApi } from "@/lib/api";
import { useAuth } from "@/hooks/useAuth";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const ProjectDetail = () => {
  const { id } = useParams<{ id: string }>();
  const { user, isAuthenticated } = useAuth();
  const [project, setProject] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Data for modals
  const [allUsers, setAllUsers] = useState<any[]>([]);
  const [allTopics, setAllTopics] = useState<any[]>([]);

  // State for modals
  const [selectedUser, setSelectedUser] = useState<string>("");
  const [selectedTopic, setSelectedTopic] = useState<string>("");

  useEffect(() => {
    let active = true;
    if (!id) {
      setProject(null);
      setLoading(false);
      return;
    }
    const fetchProject = async () => {
      try {
        const p = await projectsApi.get(id);
        if (!active) return;
        setProject(p);
      } catch (err) {
        setError("Failed to load project details.");
        setProject(null);
      }
    };

    const fetchManagementData = async () => {
      if (!isAuthenticated) return;
      try {
        const [users, topics] = await Promise.all([
          usersApi.list(),
          topicsApi.list(),
        ]);
        if (!active) return;
        setAllUsers(users);
        setAllTopics(topics);
      } catch (err) {
        setError("Failed to load management data. Some features may be disabled.");
      }
    };

    const loadData = async () => {
      setLoading(true);
      await Promise.all([fetchProject(), fetchManagementData()]);
      if (active) setLoading(false);
    };

    loadData();
    return () => {
      active = false;
    };
  }, [id, isAuthenticated]);

  const handleAddMember = async () => {
    if (!id || !selectedUser) return;
    const originalProject = project;
    const userToAdd = allUsers.find(u => u.id === selectedUser);
    if (!userToAdd) return;

    const updatedProject = {
      ...project,
      members: [...(project.members || []), { user: userToAdd }],
    };
    setProject(updatedProject);

    try {
      await projectsApi.addMembers(id, [selectedUser]);
      setSelectedUser("");
    } catch (err) {
      setError("Failed to add member. Please try again.");
      setProject(originalProject);
    }
  };

  const handleRemoveMember = async (memberId: string) => {
    if (!id) return;
    const originalProject = project;
    const updatedProject = {
      ...project,
      members: project.members.filter((m: any) => m.user.id !== memberId),
    };
    setProject(updatedProject);

    try {
      await projectsApi.removeMembers(id, [memberId]);
    } catch (err) {
      setError("Failed to remove member. Please try again.");
      setProject(originalProject);
    }
  };

  const handleAddTopic = async () => {
    if (!id || !selectedTopic) return;
    const originalProject = project;
    const topicToAdd = allTopics.find(t => t.id === selectedTopic);
    if (!topicToAdd) return;

    const updatedProject = {
      ...project,
      topics: [...(project.topics || []), { topic: topicToAdd }],
    };
    setProject(updatedProject);

    try {
      await projectsApi.addTopics(id, [selectedTopic]);
      setSelectedTopic("");
    } catch (err) {
      setError("Failed to add topic. Please try again.");
      setProject(originalProject);
    }
  };

  const handleRemoveTopic = async (topicId: string) => {
    if (!id) return;
    const originalProject = project;
    const updatedProject = {
      ...project,
      topics: project.topics.filter((t: any) => t.topic.id !== topicId),
    };
    setProject(updatedProject);

    try {
      await projectsApi.removeTopics(id, [topicId]);
    } catch (err) {
      setError("Failed to remove topic. Please try again.");
      setProject(originalProject);
    }
  };

  // PDF Download Function
  const downloadProjectPDF = () => {
    if (!project) return;

    // Create new PDF document
    const pdf = new jsPDF();
    const pageWidth = pdf.internal.pageSize.getWidth();
    const margin = 20;
    const maxWidth = pageWidth - 2 * margin;
    let yPosition = 30;

    // Helper function to add text with word wrapping
    const addText = (
      text: string,
      fontSize: number = 12,
      isBold: boolean = false
    ) => {
      pdf.setFontSize(fontSize);
      if (isBold) {
        pdf.setFont("helvetica", "bold");
      } else {
        pdf.setFont("helvetica", "normal");
      }

      const lines = pdf.splitTextToSize(text, maxWidth);

      // Check if we need a new page
      if (
        yPosition + lines.length * fontSize * 0.5 >
        pdf.internal.pageSize.getHeight() - 20
      ) {
        pdf.addPage();
        yPosition = 30;
      }

      pdf.text(lines, margin, yPosition);
      yPosition += lines.length * fontSize * 0.5 + 5;
    };

    // Add header
    pdf.setFillColor(0, 70, 200); // Blue background
    pdf.rect(0, 0, pageWidth, 25, "F");
    pdf.setTextColor(255, 255, 255); // White text
    pdf.setFontSize(18);
    pdf.setFont("helvetica", "bold");
    pdf.text("MESL - PROJECT DETAILS", margin, 18);

    // Reset text color
    pdf.setTextColor(0, 0, 0);
    yPosition = 40;

    // Project Title
    addText(String(project.title || "Untitled Project"), 16, true);
    yPosition += 5;

    // Add separator line
    pdf.setDrawColor(0, 245, 255);
    pdf.setLineWidth(1);
    pdf.line(margin, yPosition, pageWidth - margin, yPosition);
    yPosition += 15;

    // Overview
    addText("OVERVIEW", 14, true);
    addText(String(project.description || "No description provided."), 11);
    yPosition += 5;

    // Detailed Description
    if (project.detailedDescription) {
      addText("DETAILED DESCRIPTION", 14, true);
      addText(String(project.detailedDescription), 11);
      yPosition += 5;
    }

    // Objectives
    if (project.objectives) {
      addText("OBJECTIVES", 14, true);
      addText(String(project.objectives), 11);
      yPosition += 5;
    }

    // Methodology
    if (project.methodology) {
      addText("METHODOLOGY", 14, true);
      addText(String(project.methodology), 11);
      yPosition += 5;
    }

    // Technologies
    if (project.technologies) {
      const techs = Array.isArray(project.technologies)
        ? project.technologies
        : String(project.technologies || "").split(",").filter(Boolean);
      addText("TECHNOLOGIES USED", 14, true);
      addText(techs.join(", "), 11);
      yPosition += 5;
    }

    // Project Information
    addText("PROJECT INFORMATION", 14, true);
    if (project.category) addText(`Category: ${project.category}`, 11);
    if (project.status) addText(`Status: ${project.status}`, 11);
    if (project.teamSize) addText(`Team Size: ${project.teamSize} members`, 11);
    if (project.duration) addText(`Duration: ${project.duration}`, 11);
    yPosition += 5;

    // Team Members
    if (project.teamMembers && project.teamMembers.length) {
      addText("TEAM MEMBERS", 14, true);
      project.teamMembers.forEach((member: any) => {
        addText(`• ${member.name} - ${member.role}`, 11);
      });
      yPosition += 5;
    }

    // Links
    if (project.githubUrl || project.demoUrl) {
      addText("PROJECT LINKS", 14, true);
      if (project.githubUrl) {
        addText(`GitHub: ${project.githubUrl}`, 11);
      }
      if (project.demoUrl) {
        addText(`Demo: ${project.demoUrl}`, 11);
      }
      yPosition += 5;
    }

    // Footer
    const footerY = pdf.internal.pageSize.getHeight() - 15;
    pdf.setFontSize(10);
    pdf.setFont("helvetica", "italic");
    pdf.setTextColor(128, 128, 128);
    pdf.text(
      `Generated on: ${new Date().toLocaleDateString()}`,
      margin,
      footerY
    );
    pdf.text(
      "MESL - Mechatronics & Embedded System Lab",
      pageWidth - margin - 80,
      footerY
    );

    // Save the PDF
    pdf.save(`${String(project.title || "Project").replace(/\s+/g, "_")}_Details.pdf`);
  };

  if (loading) {
    return (
      <Layout>
        <div className="page-content container mx-auto px-4 py-8">
          {error && (
            <div className="project-detail-card p-4 mb-6 border-red-500/30 bg-red-500/10 text-red-300">
              {error}
            </div>
          )}
          <div className="text-center">
            <h1 className="font-['Orbitron'] text-2xl font-bold text-[#00F5FF] mb-4">
              Loading project...
            </h1>
          </div>
        </div>
      </Layout>
    );
  }

  if (!project) {
    return (
      <Layout>
        <div className="page-content container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="font-['Orbitron'] text-4xl font-bold text-[#00F5FF] mb-4">
              Project Not Found
            </h1>
            <p className="text-[--text-secondary] mb-8">
              The requested project could not be found.
            </p>
            <Link to="/projects">
              <Button className="bg-[var(--accent)] text-[var(--text)] hover:bg-[var(--secondary)] hover:text-[var(--text)] transition-colors">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Projects
              </Button>
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="page-content container mx-auto px-4 py-8">
        {/* Back Button */}
        <div className="mb-8">
          <Link to="/projects">
            <Button
              variant="outline"
              className="border-[#00F5FF] text-[#00F5FF] hover:bg-[#00F5FF] hover:text-[#001233]"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Projects
            </Button>
          </Link>
        </div>

        {/* Project Header */}
        <div className="mb-12">
          <div className="flex flex-wrap items-center gap-2 mb-4">
            {project.status && (
              <span
                className={`px-3 py-1 rounded-full text-sm font-medium ${
                  project.status === "Active"
                    ? "bg-green-500/20 text-green-400 border border-green-500/30"
                    : project.status === "Completed"
                    ? "bg-blue-500/20 text-blue-400 border border-blue-500/30"
                    : "bg-yellow-500/20 text-yellow-400 border border-yellow-500/30"
                }`}
              >
                {project.status}
              </span>
            )}
            {project.category && (
              <span className="px-3 py-1 rounded-full text-sm font-medium bg-[#0466C8]/20 text-[--text-secondary] border border-[#0466C8]/30">
                {project.category}
              </span>
            )}
          </div>

          <h1 className="font-['Orbitron'] text-4xl md:text-5xl font-bold text-[#00F5FF] mb-6">
            {project.title || "Untitled Project"}
          </h1>

          {project.overview && (
            <p className="text-xl font-['Rajdhani'] text-[--text-secondary] leading-relaxed max-w-4xl">
              {project.overview}
            </p>
          )}
        </div>

        {/* Project Image */}
        {project.image || project.hero_image ? (
          <div className="mb-12">
            <div className="relative overflow-hidden rounded-xl border border-[#00F5FF]/20">
              <img
                src={project.image || project.hero_image}
                alt={project.title}
                className="w-full h-96 object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-[#001233]/60 to-transparent"></div>
            </div>
          </div>
        ) : null}

        {/* Project Details Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Detailed Description */}
            {(project.detailedDescription || project.objectives || project.methodology) && (
              <div className="project-detail-card p-6">
                <h2 className="font-['Orbitron'] text-2xl font-bold text-[#00F5FF] mb-4">
                  Project Overview
                </h2>
                <div className="space-y-4 text-[--text-secondary] font-['Rajdhani'] text-lg leading-relaxed">
                  {project.detailedDescription && <p>{project.detailedDescription}</p>}
                  {project.objectives && <p>{project.objectives}</p>}
                  {project.methodology && <p>{project.methodology}</p>}
                </div>
              </div>
            )}

            {/* Technologies Used */}
            {/* Project Topics */}
            {project.topics && (
              <div className="project-detail-card p-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="font-['Orbitron'] text-2xl font-bold text-[#00F5FF]">
                    Project Topics
                  </h2>
                  {isAuthenticated && (
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button size="sm" className="bg-transparent border border-[#00F5FF] text-[#00F5FF] hover:bg-[#00F5FF] hover:text-black">
                          <PlusCircle className="h-4 w-4 mr-2" />
                          Add Topic
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Add Topic to Project</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                          <Select onValueChange={setSelectedTopic} value={selectedTopic}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a topic" />
                            </SelectTrigger>
                            <SelectContent>
                              {allTopics.map((topic) => (
                                <SelectItem key={topic.id} value={topic.id}>
                                  {topic.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <Button onClick={handleAddTopic} disabled={!selectedTopic}>Add Topic</Button>
                        </div>
                      </DialogContent>
                    </Dialog>
                  )}
                </div>
                <div className="flex flex-wrap gap-3">
                  {project.topics.map((t: any, index: number) => (
                    <span
                      key={index}
                      className="flex items-center px-4 py-2 bg-[#0466C8]/20 text-[#00F5FF] border border-[#0466C8]/30 rounded-lg font-['Rajdhani'] font-medium"
                    >
                      {t.topic.name}
                      {isAuthenticated && (
                        <button onClick={() => handleRemoveTopic(t.topic.id)} className="ml-2 text-red-400 hover:text-red-600">
                          <X className="h-4 w-4" />
                        </button>
                      )}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Project Gallery */}
            {project.gallery && Array.isArray(project.gallery) && project.gallery.length > 0 && (
              <div className="project-detail-card p-6">
                <h2 className="font-['Orbitron'] text-2xl font-bold text-[#00F5FF] mb-4">
                  Project Gallery
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {project.gallery.map((image: string, index: number) => (
                    <div
                      key={index}
                      className="relative overflow-hidden rounded-lg border border-[#00F5FF]/20"
                    >
                      <img
                        src={image}
                        alt={`${project.title} - Image ${index + 1}`}
                        className="w-full h-48 object-cover hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Project Info */}
            {(project.duration || project.teamSize) && (
              <div className="project-detail-card p-6">
                <h3 className="font-['Orbitron'] text-xl font-bold text-[#00F5FF] mb-4">
                  Project Information
                </h3>
                <div className="space-y-4">
                  {project.duration && (
                    <div className="flex items-center text-white/80">
                      <Calendar className="mr-3 h-5 w-5 text-[#00F5FF]" />
                      <div>
                        <p className="font-medium">Duration</p>
                        <p className="text-sm text-white/60">{project.duration}</p>
                      </div>
                    </div>
                  )}
                  {project.teamSize && (
                    <div className="flex items-center text-white/80">
                      <Users className="mr-3 h-5 w-5 text-[#00F5FF]" />
                      <div>
                        <p className="font-medium">Team Size</p>
                        <p className="text-sm text-white/60">
                          {project.teamSize} members
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Team Members */}
            <div className="project-detail-card p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="font-['Orbitron'] text-xl font-bold text-[#00F5FF]">
                  Team Members
                </h3>
                {isAuthenticated && (
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button size="sm" className="bg-transparent border border-[#00F5FF] text-[#00F5FF] hover:bg-[#00F5FF] hover:text-black">
                        <PlusCircle className="h-4 w-4 mr-2" />
                        Add Member
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Add Member to Project</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <Select onValueChange={setSelectedUser} value={selectedUser}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a user" />
                          </SelectTrigger>
                          <SelectContent>
                            {allUsers.map((u) => (
                              <SelectItem key={u.id} value={u.id}>
                                {u.full_name || u.username}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <Button onClick={handleAddMember} disabled={!selectedUser}>Add Member</Button>
                      </div>
                    </DialogContent>
                  </Dialog>
                )}
              </div>
              <div className="space-y-3">
                {project.members && project.members.map((member: any, index: number) => (
                  <div key={index} className="flex items-center justify-between space-x-3">
                    <div className="flex items-center space-x-3">
                      <div>
                        <p className="font-medium text-white">{member.user.full_name || member.user.username}</p>
                      </div>
                    </div>
                    {isAuthenticated && (
                      <button onClick={() => handleRemoveMember(member.user.id)} className="text-red-400 hover:text-red-600">
                        <X className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                ))}
                {(!project.members || project.members.length === 0) && (
                  <p className="text-sm text-white/60">No members assigned yet.</p>
                )}
              </div>
            </div>

            {/* Project Links & Actions */}
            {(project.githubUrl || project.demoUrl) && (
              <div className="project-detail-card p-6">
                <h3 className="font-['Orbitron'] text-xl font-bold text-[#00F5FF] mb-4">
                  Project Links & Actions
                </h3>
                <div className="space-y-3">
                  {/* PDF Download Button */}
                  <div className="border border-[#00F5FF]/30 rounded-lg p-3 bg-[#00F5FF]/5">
                    <button
                      onClick={downloadProjectPDF}
                      className="flex items-center text-white/90 hover:text-[#00F5FF] transition-colors w-full text-left font-medium"
                    >
                      <FileText className="mr-3 h-5 w-5 text-[#00F5FF]" />
                      Download Project Details as PDF
                      <Download className="ml-auto h-4 w-4" />
                    </button>
                    <p className="text-xs text-white/60 mt-1 ml-8">
                      Get a comprehensive PDF report with all project information
                    </p>
                  </div>

                  {project.githubUrl && (
                    <a
                      href={project.githubUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center text-white/80 hover:text-[#00F5FF] transition-colors"
                    >
                      <Github className="mr-3 h-5 w-5" />
                      View Source Code
                      <ExternalLink className="ml-2 h-4 w-4" />
                    </a>
                  )}
                  {project.demoUrl && (
                    <a
                      href={project.demoUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center text-white/80 hover:text-[#00F5FF] transition-colors"
                    >
                      <ExternalLink className="mr-3 h-5 w-5" />
                      Live Demo
                      <ExternalLink className="ml-2 h-4 w-4" />
                    </a>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default ProjectDetail;
