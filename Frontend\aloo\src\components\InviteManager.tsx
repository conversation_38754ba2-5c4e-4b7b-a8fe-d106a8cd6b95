import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import AdminInvite from "./AdminInvite";
import { 
  IoMail, 
  IoTime, 
  IoCheckmarkCircle, 
  IoCloseCircle,
  IoRefresh 
} from "react-icons/io5";

interface PendingInvite {
  id: string;
  email: string;
  created_at: string;
  expires_at: string;
  status: "pending" | "accepted" | "expired";
}

const InviteManager = () => {
  const [invites, setInvites] = useState<PendingInvite[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadInvites = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = sessionStorage.getItem("access_token");
      if (!token) {
        throw new Error("Authentication required");
      }

      // Note: This endpoint might not exist yet in the backend
      // You may need to implement it or use a different approach
      const response = await fetch("http://localhost:8000/auth/invites", {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          // Endpoint doesn't exist yet, show mock data or empty state
          setInvites([]);
          return;
        }
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || "Failed to load invitations");
      }

      const data = await response.json();
      setInvites(data);

    } catch (err: any) {
      console.error("Failed to load invites:", err);
      setError(err.message);
      // For now, show empty state if endpoint doesn't exist
      if (err.message.includes("404") || err.message.includes("Failed to fetch")) {
        setInvites([]);
        setError(null);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadInvites();
  }, []);

  const handleInviteSent = (email: string) => {
    // Add the new invite to the list optimistically
    const newInvite: PendingInvite = {
      id: Date.now().toString(),
      email,
      created_at: new Date().toISOString(),
      expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
      status: "pending",
    };
    setInvites(prev => [newInvite, ...prev]);
  };

  const getStatusBadge = (invite: PendingInvite) => {
    const now = new Date();
    const expiresAt = new Date(invite.expires_at);
    
    if (invite.status === "accepted") {
      return (
        <Badge className="bg-green-500/20 text-green-300 border-green-500/30">
          <IoCheckmarkCircle className="w-3 h-3 mr-1" />
          Accepted
        </Badge>
      );
    }
    
    if (now > expiresAt || invite.status === "expired") {
      return (
        <Badge className="bg-red-500/20 text-red-300 border-red-500/30">
          <IoCloseCircle className="w-3 h-3 mr-1" />
          Expired
        </Badge>
      );
    }
    
    return (
      <Badge className="bg-yellow-500/20 text-yellow-300 border-yellow-500/30">
        <IoTime className="w-3 h-3 mr-1" />
        Pending
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <Card className="project-detail-card">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <IoMail className="w-5 h-5 text-[#00F5FF]" />
            <span className="font-['Orbitron'] text-[#00F5FF]">
              User Invitations
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              onClick={loadInvites}
              variant="outline"
              size="sm"
              className="border-[var(--secondary)]/50 text-[var(--text)] hover:bg-[var(--secondary)]/20"
            >
              <IoRefresh className="w-4 h-4" />
            </Button>
            <AdminInvite onInviteSent={handleInviteSent} />
          </div>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="p-6">
        {error && (
          <Alert className="mb-4 border-red-500/30 bg-red-500/10">
            <AlertDescription className="text-red-300">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {loading ? (
          <div className="text-center py-8">
            <div className="text-[#00F5FF]">Loading invitations...</div>
          </div>
        ) : invites.length === 0 ? (
          <div className="text-center py-8">
            <IoMail className="w-12 h-12 text-white/30 mx-auto mb-4" />
            <p className="text-white/60 font-['Rajdhani']">
              No invitations sent yet. Use the "Invite User" button to send your first invitation.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {invites.map((invite) => (
              <div
                key={invite.id}
                className="flex items-center justify-between p-4 bg-[var(--background)]/50 rounded-lg border border-[var(--secondary)]/30"
              >
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <span className="font-['Orbitron'] text-white font-medium">
                      {invite.email}
                    </span>
                    {getStatusBadge(invite)}
                  </div>
                  <div className="text-sm text-white/60 font-['Rajdhani']">
                    <div>Sent: {formatDate(invite.created_at)}</div>
                    <div>Expires: {formatDate(invite.expires_at)}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default InviteManager;
