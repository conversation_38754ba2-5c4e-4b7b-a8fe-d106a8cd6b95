import { useEffect, useState } from "react";
import { <PERSON> } from "react-router-dom";
import Layout from "@/components/Layout";
import { eventsApi } from "@/lib/api";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Calendar,
  MapPin,
  Users,
  Clock,
  Search,
  Filter,
  ChevronRight,
  CalendarDays,
  History,
  Globe,
} from "lucide-react";
import { Input } from "@/components/ui/input";

const Events = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");

  // Load events from backend and show error if unavailable
  const [allEvents, setAllEvents] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);

  
  useEffect(() => {
    let active = true;
    eventsApi
      .list()
      .then((list) => {
        if (!active) return;
        const mapped = list.map((e: any) => ({
          id: e.id,
          title: e.title,
          date: e.date,
          time: e.time,
          location: e.location,
          description: e.description,
          status: e.status || "upcoming",
          image: "",
          tags: Array.isArray(e.topics) ? e.topics.map((t: any) => t.name || t.id) : [],
          category: "Event",
        }));
        setAllEvents(mapped);
      })
      .catch(() => {
        setError("Unable to load events right now. Please try again later.");
        setAllEvents([]);
      });
    return () => {
      active = false;
    };
  }, []);

  const categories = [
    "all",
    "Workshop",
    "Seminar",
    "Conference",
    "Symposium",
    "Networking",
  ];

  const filterEvents = (events: typeof allEvents, status?: string) => {
    return events.filter((event) => {
      const title = String(event.title || "");
      const description = String(event.description || "");
      const tags = Array.isArray(event.tags) ? event.tags : [];

      const matchesSearch =
        title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesCategory =
        selectedCategory === "all" || event.category === selectedCategory;
      const matchesStatus = !status || event.status === status;

      return matchesSearch && matchesCategory && matchesStatus;
    });
  };

  const upcomingEvents = filterEvents(allEvents, "upcoming");
  const pastEvents = filterEvents(allEvents, "archived");
  const filteredAllEvents = filterEvents(allEvents);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "upcoming":
        return "bg-green-500/20 text-green-400 border-green-500/30";
      case "archived":
        return "bg-gray-500/20 text-gray-400 border-gray-500/30";
      case "ongoing":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30";
      default:
        return "bg-blue-500/20 text-blue-400 border-blue-500/30";
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "Workshop":
        return "bg-purple-500/20 text-purple-400 border-purple-500/30";
      case "Seminar":
        return "bg-orange-500/20 text-orange-400 border-orange-500/30";
      case "Conference":
        return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      case "Symposium":
        return "bg-pink-500/20 text-pink-400 border-pink-500/30";
      case "Networking":
        return "bg-green-500/20 text-green-400 border-green-500/30";
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  const EventCard = ({ event }: { event: (typeof allEvents)[0] }) => (
    <Card className="project-detail-card group hover:border-[#00F5FF]/50 transition-all duration-300">
      <div className="relative overflow-hidden">
        <img
          src={event.image}
          alt={event.title}
          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
        />
        <div className="absolute top-4 left-4 flex gap-2">
          <Badge className={getStatusColor(event.status)}>
            {event.status === "upcoming"
              ? "Upcoming"
              : event.status === "ongoing"
              ? "Ongoing"
              : "Past"}
          </Badge>
          <Badge className={getCategoryColor(event.category)}>
            {event.category}
          </Badge>
        </div>
      </div>

      <CardHeader>
        <h3 className="font-['Orbitron'] text-xl font-bold text-[#00F5FF] group-hover:text-[#00F5FF]/80 transition-colors">
          {event.title}
        </h3>
        <p className="text-[var(--text-secondary)] font-['Rajdhani'] text-sm line-clamp-2">
          {event.description}
        </p>
      </CardHeader>

      <CardContent>
        <div className="space-y-3 mb-4">
          <div className="flex items-center text-[var(--text-secondary)] text-sm">
            <Calendar className="mr-2 h-4 w-4 text-[#00F5FF]" />
            <span>
              {event.date ? new Date(event.date).toLocaleDateString() : "TBA"}
              {event.time ? ` at ${event.time}` : ""}
            </span>
          </div>
          <div className="flex items-center text-[var(--text-secondary)] text-sm">
            <MapPin className="mr-2 h-4 w-4 text-[#00F5FF]" />
            <span>{event.location || "TBA"}</span>
          </div>
          {(typeof event.registered !== "undefined" || typeof event.capacity !== "undefined") && (
            <div className="flex items-center text-[var(--text-secondary)] text-sm">
              <Users className="mr-2 h-4 w-4 text-[#00F5FF]" />
              <span>
                {typeof event.registered !== "undefined" ? event.registered : "-"}
                {typeof event.capacity !== "undefined" ? `/${event.capacity} registered` : " registered"}
              </span>
            </div>
          )}
          {event.organizer && (
            <div className="flex items-center text-[var(--text-secondary)] text-sm">
              <Clock className="mr-2 h-4 w-4 text-[#00F5FF]" />
              <span>Organized by {event.organizer}</span>
            </div>
          )}
        </div>

        {Array.isArray(event.tags) && event.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-4">
            {event.tags.map((tag: string, index: number) => (
              <Badge
                key={index}
                className="bg-[#0466C8]/20 text-[#00F5FF] border border-[#0466C8]/30 text-xs"
              >
                {tag}
              </Badge>
            ))}
          </div>
        )}

        <Button
          asChild
          size="sm"
          className="w-full bg-[var(--accent)] text-[var(--text)] hover:bg-[var(--secondary)] hover:text-[var(--text)] transition-colors"
        >
          <Link
            to={`/events/${event.id}`}
            className="flex items-center justify-center"
          >
            View Details <ChevronRight className="ml-1 h-4 w-4" />
          </Link>
        </Button>
      </CardContent>
    </Card>
  );

  return (
    <Layout>
      <div className="page-content">
        {/* Hero Section */}
        <section className="hero-section">
          <div className="container">
            <div className="text-center mb-12">
              <h1 className="font-['Orbitron'] text-4xl md:text-5xl font-bold text-[#00F5FF] mb-4">
                Events & Activities
              </h1>
              <p className="text-xl text-[var(--text-secondary)] font-['Rajdhani'] max-w-3xl mx-auto">
                Join our community of researchers and innovators through
                workshops, seminars, conferences, and networking events designed
                to advance knowledge and foster collaboration.
              </p>
            </div>
          </div>
        </section>

        {/* Search and Filter Section */}
        <section className="py-8">
          <div className="container">
            {error && (
              <div className="project-detail-card p-4 mb-6 border-red-500/30 bg-red-500/10 text-red-300">
                {error}
              </div>
            )}
            <div className="flex flex-col md:flex-row gap-4 mb-8">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[var(--text-secondary)] h-4 w-4" />
                <Input
                  placeholder="Search events by title, description, or tags..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-[var(--background)] border-[var(--secondary)]/50 text-[var(--text)]"
                />
              </div>
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-[var(--text-secondary)]" />
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="bg-[var(--card-bg)] border border-[var(--border)] text-[var(--text)] rounded-md px-3 py-2 hover:border-[var(--secondary)] focus:border-[var(--secondary)] focus:outline-none transition-colors"
                >
                  {categories.map((category) => (
                    <option
                      key={category}
                      value={category}
                      className="bg-[var(--card-bg)] text-[var(--text)]"
                    >
                      {category === "all" ? "All Categories" : category}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Events Tabs */}
            <Tabs defaultValue="all" className="w-full">
              <TabsList className="grid w-full grid-cols-3 bg-[var(--card-bg)] border border-[var(--border)]">
                <TabsTrigger
                  value="all"
                  className="data-[state=active]:bg-[#0466C8] data-[state=active]:text-white flex items-center gap-2"
                >
                  <Globe className="h-4 w-4" />
                  All Events ({filteredAllEvents.length})
                </TabsTrigger>
                <TabsTrigger
                  value="upcoming"
                  className="data-[state=active]:bg-[#0466C8] data-[state=active]:text-white flex items-center gap-2"
                >
                  <CalendarDays className="h-4 w-4" />
                  Upcoming ({upcomingEvents.length})
                </TabsTrigger>
                <TabsTrigger
                  value="past"
                  className="data-[state=active]:bg-[#0466C8] data-[state=active]:text-white flex items-center gap-2"
                >
                  <History className="h-4 w-4" />
                  Past Events ({pastEvents.length})
                </TabsTrigger>
              </TabsList>

              {/* All Events Tab */}
              <TabsContent value="all" className="mt-8">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredAllEvents.length > 0 ? (
                    filteredAllEvents.map((event) => (
                      <EventCard key={event.id} event={event} />
                    ))
                  ) : (
                    <div className="col-span-full text-center py-12">
                      <Calendar className="mx-auto h-12 w-12 text-[var(--text-secondary)] mb-4" />
                      <h3 className="font-['Orbitron'] text-xl font-semibold text-[var(--text-secondary)] mb-2">
                        No events found
                      </h3>
                      <p className="text-[var(--text-secondary)]">
                        Try adjusting your search criteria or category filter.
                      </p>
                    </div>
                  )}
                </div>
              </TabsContent>

              {/* Upcoming Events Tab */}
              <TabsContent value="upcoming" className="mt-8">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {upcomingEvents.length > 0 ? (
                    upcomingEvents.map((event) => (
                      <EventCard key={event.id} event={event} />
                    ))
                  ) : (
                    <div className="col-span-full text-center py-12">
                      <CalendarDays className="mx-auto h-12 w-12 text-[var(--text-secondary)] mb-4" />
                      <h3 className="font-['Orbitron'] text-xl font-semibold text-[var(--text-secondary)] mb-2">
                        No upcoming events
                      </h3>
                      <p className="text-[var(--text-secondary)]">
                        Check back soon for new events and workshops.
                      </p>
                    </div>
                  )}
                </div>
              </TabsContent>

              {/* Past Events Tab */}
              <TabsContent value="past" className="mt-8">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {pastEvents.length > 0 ? (
                    pastEvents.map((event) => (
                      <EventCard key={event.id} event={event} />
                    ))
                  ) : (
                    <div className="col-span-full text-center py-12">
                      <History className="mx-auto h-12 w-12 text-[var(--text-secondary)] mb-4" />
                      <h3 className="font-['Orbitron'] text-xl font-semibold text-[var(--text-secondary)] mb-2">
                        No past events found
                      </h3>
                      <p className="text-[var(--text-secondary)]">
                        Past events will appear here after they conclude.
                      </p>
                    </div>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </section>
      </div>
    </Layout>
  );
};

export default Events;
