import { useEffect } from "react";
import Navbar from "./Navbar";
import Footer from "./Footer";
import { useTheme } from "@/contexts/ThemeContext";

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { isDarkTheme, toggleTheme } = useTheme();

  useEffect(() => {
    // Create the interactive grid background
    const createInteractiveGrid = () => {
      // Remove existing backgrounds
      const existing = document.getElementById("interactive-grid");
      if (existing) existing.remove();

      // Create background container
      const background = document.createElement("div");
      background.id = "interactive-grid";
      background.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        display: grid;
        grid-template-columns: repeat(20, 1fr);
        grid-template-rows: repeat(20, 1fr);
        pointer-events: none;
      `;

      // Create grid cells
      const cells: HTMLDivElement[] = [];
      for (let i = 0; i < 400; i++) {
        const cell = document.createElement("div");
        cell.style.cssText = `
          border: 1px solid ${
            isDarkTheme ? "rgba(0, 245, 255, 0.05)" : "rgba(0, 120, 212, 0.05)"
          };
          transition: background-color 0.3s ease;
          background: transparent;
        `;
        background.appendChild(cell);
        cells.push(cell);
      }

      document.body.appendChild(background);

      // Mouse tracking - subtle grid effect only
      const handleMouseMove = (e: MouseEvent) => {
        const rect = background.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        const cellWidth = rect.width / 20;
        const cellHeight = rect.height / 20;
        const gridX = Math.floor(x / cellWidth);
        const gridY = Math.floor(y / cellHeight);

        cells.forEach((cell, index) => {
          const cellGridX = index % 20;
          const cellGridY = Math.floor(index / 20);

          const distance = Math.sqrt(
            Math.pow(cellGridX - gridX, 2) + Math.pow(cellGridY - gridY, 2)
          );

          // Very subtle effect - smaller radius and lower intensity
          if (distance <= 2) {
            const intensity = Math.max(0, 1 - distance / 2);
            const color = isDarkTheme
              ? `rgba(0, 245, 255, ${intensity * 0.1})`
              : `rgba(0, 120, 212, ${intensity * 0.08})`;

            cell.style.backgroundColor = color;
            cell.style.transition = "background-color 0.1s ease";
          } else {
            cell.style.backgroundColor = "transparent";
            cell.style.transition = "background-color 0.3s ease";
          }
        });
      };

      const handleMouseLeave = () => {
        cells.forEach((cell) => {
          cell.style.backgroundColor = "transparent";
          cell.style.transition = "background-color 0.5s ease";
        });
      };

      // Add event listeners
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseleave", handleMouseLeave);

      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseleave", handleMouseLeave);
        background.remove();
      };
    };

    // Create subtle floating particles
    const createFloatingParticles = () => {
      for (let i = 0; i < 15; i++) {
        const particle = document.createElement("div");
        particle.className = "floating-particle";
        particle.style.cssText = `
          position: fixed;
          width: 5px;
          height: 5px;
          border-radius: 50%;
          background: ${
            isDarkTheme ? "rgba(0, 245, 255, 0.4)" : "rgba(0, 120, 212, 0.4)"
          };
          pointer-events: none;
          z-index: -1;
          animation: float ${15 + Math.random() * 10}s infinite linear;
          animation-delay: ${Math.random() * 5}s;
          left: ${Math.random() * 100}%;
          top: 100%;
          box-shadow: 0 0 5px ${
            isDarkTheme ? "rgba(0, 245, 255, 0.3)" : "rgba(0, 120, 212, 0.3)"
          };
        `;
        document.body.appendChild(particle);
      }
    };

    // Update particle colors for dark theme
    const updateParticleColors = () => {
      const currentIsDark =
        document.documentElement.classList.contains("dark-theme");
      document
        .querySelectorAll(".floating-particle")
        .forEach((particle: any) => {
          if (currentIsDark) {
            particle.style.background = "rgba(0, 245, 255, 0.4)";
            particle.style.boxShadow = "0 0 5px rgba(0, 245, 255, 0.3)";
          } else {
            particle.style.background = "rgba(0, 120, 212, 0.4)";
            particle.style.boxShadow = "0 0 5px rgba(0, 120, 212, 0.3)";
          }
        });
    };

    // Initialize background and particles
    const cleanupGrid = createInteractiveGrid();
    createFloatingParticles();

    // Listen for theme changes
    const observer = new MutationObserver(updateParticleColors);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ["class"],
    });

    return () => {
      // Clean up grid event listeners
      if (cleanupGrid) cleanupGrid();
      observer.disconnect();
      // Clean up particles
      document
        .querySelectorAll(".floating-particle")
        .forEach((p) => p.remove());
    };
  }, [isDarkTheme]);


  return (
    <>
      <Navbar />
      <main>{children}</main>
      <Footer />

      {/* Theme Toggle Button */}
      <button
        id="theme-toggle"
        className="theme-toggle"
        aria-label="Toggle theme"
        onClick={toggleTheme}
      >
        <span className="theme-toggle-icon">{isDarkTheme ? "🌙" : "☀️"}</span>
      </button>
    </>
  );
};

export default Layout;
