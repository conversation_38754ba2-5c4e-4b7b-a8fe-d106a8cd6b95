import LoginModal from "./LoginModal";
import { useAuth } from "../hooks/useAuth";

interface LoginButtonProps {
  children: React.ReactNode;
  className?: string;
  variant?: "login" | "signup";
}

const LoginButton: React.FC<LoginButtonProps> = ({ 
  children, 
  className = "", 
  variant = "login" 
}) => {
  const { login } = useAuth();

  const handleLoginSuccess = (userData: any) => {
    login(userData);
    console.log(`${variant} successful:`, userData);
  };

  return (
    <LoginModal onLoginSuccess={handleLoginSuccess} initialMode={variant}>
      <button className={className}>
        {children}
      </button>
    </LoginModal>
  );
};

export default LoginButton;