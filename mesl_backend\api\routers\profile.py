from pydantic import BaseModel, HttpUrl, EmailStr
import uuid
from models import User,Department,CommunityType,UserStatus,ProjectStatus, Project, ProjectMember
from api.deps import CurrentUser,SessionDep, CurrentUser,SupabaseDep
from typing import List, Optional
from fastapi import APIRouter, HTTPException, UploadFile, File
from sqlmodel import select
import logging 


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/users", tags=["users"])

BUCKET_NAME='pfp'

class UserProfileUpdate(BaseModel):
    full_name: Optional[str] = None
    username: Optional[str] = None
    github_link: Optional[HttpUrl] = None
    linkedin_link: Optional[HttpUrl] = None
    gmail: Optional[EmailStr] = None
    contact: Optional[str] = None
    phone: Optional[str] = None
    bio: Optional[str] = None
    skills: Optional[List[str]] = None


class UserProjectRead(BaseModel):
    id: uuid.UUID
    title: str
    status: ProjectStatus

    class Config:
        from_attributes = True


class UserProfileRead(BaseModel):
    id: uuid.UUID
    full_name: str
    username: str
    department: Department
    community_type: CommunityType
    is_admin: bool
    status: UserStatus

    profile_pic: Optional[str] = None  

    github_link: Optional[str]
    linkedin_link: Optional[str]
    gmail: Optional[str]
    contact: Optional[str]
    phone: Optional[str]
    bio: Optional[str]
    skills: Optional[List[str]]

    projects: List[UserProjectRead] = []

    class Config:
        from_attributes = True


@router.get("/me", response_model=UserProfileRead)
def get_my_profile(session: SessionDep, current_user: CurrentUser):
    user = session.get(User, current_user.id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    projects = session.exec(
        select(Project).join(ProjectMember).where(ProjectMember.user_id == user.id)
    ).all()

    return UserProfileRead(
        **user.dict(),
        projects=projects
    )


@router.put("/me", response_model=UserProfileRead)
def update_my_profile(
    update_in: UserProfileUpdate,
    session: SessionDep,
    current_user: CurrentUser
):
    user = session.get(User, current_user.id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    update_data = update_in.model_dump(exclude_unset=True)

    # ✅ Handle username uniqueness
    if "username" in update_data and update_data["username"] != user.username:
        exists = session.exec(
            select(User).where(User.username == update_data["username"])
        ).first()
        if exists:
            raise HTTPException(status_code=400, detail="Username already taken")

    # ✅ Convert HttpUrl/EmailStr to str
    for field, value in update_data.items():
        if value is not None:
            setattr(user, field, str(value) if not isinstance(value, (list, dict)) else value)

    session.add(user)
    session.commit()
    session.refresh(user)

    projects = session.exec(
        select(Project).join(ProjectMember).where(ProjectMember.user_id == user.id)
    ).all()

    return UserProfileRead(
        **user.model_dump(),
        projects=projects
    )

@router.post("/me/profile-pic")
async def upload_profile_pic(
    session: SessionDep,
    current_user: CurrentUser,
    file: UploadFile = File(...),
    supabase: SupabaseDep = None
):
    user = session.get(User, current_user.id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    if not file or not file.filename:
        raise HTTPException(status_code=400, detail="No file uploaded")

    file_ext = file.filename.split(".")[-1]
    file_path = f"{uuid.uuid4()}.{file_ext}"

    try:
        file_content = await file.read()
        supabase.storage.from_(BUCKET_NAME).upload(path=file_path, file=file_content)
        public_url = supabase.storage.from_(BUCKET_NAME).get_public_url(file_path)
    except Exception as e:
        logger.error(f"Failed to upload profile pic: {e}")
        raise HTTPException(status_code=500, detail="Failed to upload file")

    user.profile_pic = public_url
    session.add(user)
    session.commit()
    session.refresh(user)

    return {"profile_pic": public_url}


@router.put("/me/profile-pic")
async def update_profile_pic(
    session: SessionDep,
    current_user: CurrentUser,
    file: UploadFile = File(...),
    supabase: SupabaseDep = None
):
    user = session.get(User, current_user.id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    if not file or not file.filename:
        raise HTTPException(status_code=400, detail="No file uploaded")

    file_ext = file.filename.split(".")[-1]
    file_path = f"{uuid.uuid4()}.{file_ext}"

    if user.profile_pic:
        old_path = user.profile_pic.split("/")[-1]
        supabase.storage.from_(BUCKET_NAME).remove([f"{old_path}"])


    try:
        file_content = await file.read()
        supabase.storage.from_(BUCKET_NAME).upload(path=file_path, file=file_content)
        public_url = supabase.storage.from_(BUCKET_NAME).get_public_url(file_path)
    except Exception as e:
        logger.error(f"Failed to upload profile pic: {e}")
        raise HTTPException(status_code=500, detail="Failed to upload file")

    # Update profile picture URL
    user.profile_pic = public_url
    session.add(user)
    session.commit()
    session.refresh(user)

    return {"profile_pic": public_url}
